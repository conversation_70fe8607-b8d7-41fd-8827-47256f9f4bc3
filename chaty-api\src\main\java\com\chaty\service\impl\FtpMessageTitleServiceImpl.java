package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.FtpMessageTitle;
import com.chaty.dto.FtpMessageTitleDTO;
import com.chaty.exception.BaseException;
import com.chaty.mapper.FtpMessageTitleMapper;
import com.chaty.service.FtpMessageTitleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import java.util.Objects;

@Service
public class FtpMessageTitleServiceImpl extends ServiceImpl<FtpMessageTitleMapper, FtpMessageTitle> implements FtpMessageTitleService {

    @Override
    @Transactional
    public FtpMessageTitleDTO addTitle(FtpMessageTitleDTO dto) {
        if (StrUtil.isBlank(dto.getTitle()) || StrUtil.isBlank(dto.getPath())) {
            throw new BaseException("标题和路径不能为空");
        }

        FtpMessageTitle ftpMessageTitle = new FtpMessageTitle();
        BeanUtils.copyProperties(dto, ftpMessageTitle);

        save(ftpMessageTitle);

        FtpMessageTitleDTO result = new FtpMessageTitleDTO();
        BeanUtils.copyProperties(ftpMessageTitle, result);
        return result;
    }

    @Override
    @Transactional
    public void deleteTitle(Integer id) {
        if (id == null) {
            throw new BaseException("id不能为空");
        }

        FtpMessageTitle ftpMessageTitle = getById(id);
        if (ftpMessageTitle == null) {
            throw new BaseException("标题记录不存在");
        }

        removeById(id);
    }

    @Override
    @Transactional
    public FtpMessageTitleDTO updateTitle(FtpMessageTitleDTO dto) {
        if (dto.getId() == null) {
            throw new BaseException("id不能为空");
        }

        FtpMessageTitle ftpMessageTitle = getById(dto.getId());
        if (ftpMessageTitle == null) {
            throw new BaseException("标题记录不存在");
        }

        BeanUtils.copyProperties(dto, ftpMessageTitle, "id", "createTime", "updateTime");

        updateById(ftpMessageTitle);

        FtpMessageTitleDTO result = new FtpMessageTitleDTO();
        BeanUtils.copyProperties(ftpMessageTitle, result);
        return result;
    }

    @Override
    public IPage<FtpMessageTitleDTO> selectPage(FtpMessageTitleDTO param) {
        LambdaQueryWrapper<FtpMessageTitle> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(Objects.nonNull(param.getId()), FtpMessageTitle::getId, param.getId());
        queryWrapper.like(StrUtil.isNotBlank(param.getTitle()), FtpMessageTitle::getTitle, param.getTitle());
        queryWrapper.like(StrUtil.isNotBlank(param.getPath()), FtpMessageTitle::getPath, param.getPath());
        queryWrapper.like(StrUtil.isNotBlank(param.getRemark()), FtpMessageTitle::getRemark, param.getRemark());
        queryWrapper.eq(Objects.nonNull(param.getWeight()), FtpMessageTitle::getWeight, param.getWeight());
        queryWrapper.eq(Objects.nonNull(param.getCreateTime()), FtpMessageTitle::getCreateTime, param.getCreateTime());
        queryWrapper.eq(Objects.nonNull(param.getUpdateTime()), FtpMessageTitle::getUpdateTime, param.getUpdateTime());

        // 按照weight降序排序
        queryWrapper.orderByDesc(FtpMessageTitle::getWeight);

        if (param.getPage() == null) {
            throw new BaseException("分页参数不能为空");
        }

        IPage<FtpMessageTitle> ftpMessageTitlePage = page(param.getPage().page(FtpMessageTitle.class), queryWrapper);

        return ftpMessageTitlePage.convert(entity -> {
            FtpMessageTitleDTO dto = new FtpMessageTitleDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });
    }
} 
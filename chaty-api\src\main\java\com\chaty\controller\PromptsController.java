package com.chaty.controller;

import com.chaty.common.BaseResponse;
import com.chaty.dto.UpdatePromptParam;
import com.chaty.security.AuthUtil;
import com.chaty.service.PromptsRedisService;
import org.springframework.web.bind.annotation.*;
import com.chaty.entity.User;
import javax.annotation.Resource;
import java.util.Map;

import static com.chaty.security.TokenAuthConsts.ADMIN_ROLE_ID;

@RestController
@RequestMapping("/api/prompts")
public class PromptsController {

    @Resource
    private PromptsRedisService promptsRedisService;

    @GetMapping("/list")
    public BaseResponse<?> list() {
        Map<String, Map<String, String>> res = promptsRedisService.getAllKeyListWithChineseNames();
        return BaseResponse.ok(res);
    }

    @PostMapping("/update")
    public BaseResponse<?> list(@RequestBody UpdatePromptParam param) {
        User currentUser = AuthUtil.getLoginUser();
        if (!currentUser.getRole().equals(ADMIN_ROLE_ID)) {
            return BaseResponse.error("权限不足");
        }
        promptsRedisService.updateValue(param.getKey(), param.getValue());
        return BaseResponse.ok("更新成功");
    }

    @GetMapping("/reset")
    public BaseResponse<?> list(@RequestParam("key") String key) {
        User currentUser = AuthUtil.getLoginUser();
        if (!currentUser.getRole().equals(ADMIN_ROLE_ID)) {
            return BaseResponse.error("权限不足");
        }
        promptsRedisService.resetValue(key);
        return BaseResponse.ok("重置成功");
    }


}

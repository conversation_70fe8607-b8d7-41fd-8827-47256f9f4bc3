package com.chaty.completion;

import java.util.*;
import java.math.BigDecimal;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import com.chaty.api.HuaShi.HuaShiApi;
import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.enums.*;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.service.*;
import com.chaty.service.impl.OCRServiceSelector;
import com.chaty.task.correct.CorrectCacheService;
import com.tencentcloudapi.ecc.v20181213.models.CorrectData;
import com.tencentcloudapi.ecc.v20181213.models.ECCRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Component;

import com.chaty.exception.RetryException;
import com.chaty.form.ExtraQsForm;
import com.chaty.util.FileUtil;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

import static com.chaty.task.correct.RecordCorrectorImpl.beforeCorrect;

@Slf4j
@Component
public class CompletionServiceImpl implements CompletionService {

    @Resource
    private Set<CompletionCreator> creators;
    @Resource
    private Set<BasicAiService> services;
    @Resource
    private Set<CompletionResResolver> resolvers;
    @Resource
    private AnswerCardReviewService answerCardReviewService;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private TencentEssayScoreService tencentEssayScoreService;
    @Resource
    private HuaShiApi huaShiApi;
    @Resource
    private OCRServiceSelector ocrServiceSelector;
    @Resource
    private CorrectCacheService correctCacheService;
    @Value("${correct.openai.logprobs:false}")
    private Boolean enableLogprobs;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;
    @Value("${ocr.enableOcr}")
    private Boolean enableOcr;
    @Value("${ocr.enableOcrForIdentifyOrStudentNumber}")
    private Boolean enableOcrForIdentifyOrStudentNumber;
    @Value("${ocr.enableOcrForCorrect}")
    private Boolean enableOcrForCorrect;
    @Value("${ocr.enableOcrForEssay}")
    private Boolean enableOcrForEssay;
    @Value("${ocr.enableOcrForScore}")
    private Boolean enableOcrForScore;
    @Value("${ocr.enableOcrForEXTRAQS}")
    private Boolean enableOcrForEXTRAQS;
    @Resource
    private DefaultModelRedisService defaultModelRedisService;
    @Resource
    private com.chaty.service.impl.GptAskLogServiceImpl gptAskLogService;

    private static final Map<String, String> tencentGradeMap = new HashMap<>();

    static {
        // 初始化对应的 label 和 grade 映射
        tencentGradeMap.put("一年级", "elementary");
        tencentGradeMap.put("二年级", "elementary");
        tencentGradeMap.put("三年级", "elementary");
        tencentGradeMap.put("四年级", "elementary");
        tencentGradeMap.put("五年级", "elementary");
        tencentGradeMap.put("六年级", "elementary");
        tencentGradeMap.put("初一", "grade7");
        tencentGradeMap.put("初二", "grade8");
        tencentGradeMap.put("初三", "grade9");
        tencentGradeMap.put("高一", "grade10");
        tencentGradeMap.put("高二", "grade11");
        tencentGradeMap.put("高三", "grade12");
        tencentGradeMap.put("英语四级", "cet4");
        tencentGradeMap.put("英语六级", "cet6");
    }

    //    @Retryable(value = RetryException.class, maxAttempts = 0, listeners = {"docCorrectRetryListener"}, backoff = @Backoff(delay = 1000, multiplier = 2))
    @Override
    public void correctRecordArea(DocCorrectRecordDTO record,
                                  JSONObject areaObj,
                                  JSONObject areaRes,
                                  GptAskLogEntity gptAskLogEntity) {
        String aimodel = record.getAimodel();
        DocCorrectTaskDTO task = record.getTask();
        Boolean responseFormat = task.getResponseFormat();
        Boolean jsonschema = task.getJsonschema();
        CompletionCreator creator = getCreator(aimodel, jsonschema);
        // ocr识别
        String ocrContent = null;
        if (enableOcr && enableOcrForCorrect) {
            try {
                String service = "";
                ocrContent = getOcrContent(service, areaObj.getStr("areaImg"), GetOcrContentType.CORRECT);
            } catch (InterruptedException e) {
                log.error("ocr识别失败", e);
                throw new RetryException("ocr识别失败!", e);
            }
            areaRes.set("ocrContent", ocrContent);
        }

        // 1. 请求体创建阶段
        ChatCompletionDTO completion = null;
        try {
            if (responseFormat) {
                completion = creator.createDocAreaResponseFormatCompletion(record, areaObj, areaRes, ocrContent);
            } else {
                completion = creator.createDocAreaCompletion(record, areaObj, areaRes);
            }
            completion.setTemperature(record.getTemperature()); // 设置模型 temperature 参数
            completion.setTopp(record.getTopp()); // 设置模型 top_p 参数
            if (Objects.isNull(completion.getTopp())) {
                completion.setTopp(DefaultCorrectConfigsConsts.topP);
            }
            completion.setSeed(DefaultCorrectConfigsConsts.seed);
            if (Objects.isNull(completion.getMaxTokens())) {
                completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
            }
            completion.setLogprobs(enableLogprobs);
            completion.setJsonSchema(record.getJsonschema());
            completion.setJsonObject(record.getJsonobject());
            completion.setModelRequestObj(record.getModelRequestContentObj());
        } catch (Exception e) {
            // 处理completion对象，去除base64图片
            Object completionObj = null;
            try {
                completionObj = completion;
                if (completionObj != null) {
                    com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(completionObj));
                    gptAskLogService.replaceBase64Images(json);
                    completionObj = json;
                }
            } catch (Exception ignore) {
            }
            log.error("请求体创建失败, completion对象: {}", completionObj, e);
            String errorMsg = "请求体创建失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", completion对象: " + completionObj;
            throw new RetryException(errorMsg, e);
        }

        JSONArray questions = areaObj.getJSONArray("questions");
        areaRes.set("recordId", record.getId());

        // 2. 模型调用阶段
        Map<String, Object> completionRes = null;
        int qsCount = 1;
        try {
            qsCount = areaObj.getJSONArray("questions").size();
        } catch (Exception e) {
            log.error("计算错误qsCount: ", e);
        }
        try {
            completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, qsCount);
            areaRes.set("$response", completionRes.get("$response"));
        } catch (Exception e) {
            log.error("请求模型失败", e);
            // 模型调用失败
            String errorMsg = "模型调用失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            throw new RetryException(errorMsg, e);
        }

        // 3. 响应解析阶段
        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        JSONObject resolvedRes = null;
        try {
            resolvedRes = resolver.resolveDocAreaResponseFormatRes(record, completionRes);
            // 批改结果 logprobs 获取
            if (enableLogprobs) {
                parseLogprobs(completionRes, resolvedRes);
            }
        } catch (Exception e) {
            log.error("批改结果解析失败: {}，解析对象: {}", resolvedRes, completionRes, e);
            // 响应解析失败
            String errorMsg = "响应解析失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", 解析对象: " + completionRes;
            throw new RetryException(errorMsg, e);
        }

        JSONArray reviewed = resolvedRes.getJSONArray("reviewed");
        if (reviewed.size() != questions.size()) {
            log.error("批改结果无法解析: {}, completionRes: {}", reviewed, completionRes);
            String errorMsg = "批改结果无法解析! reviewed: " + reviewed + ", completionRes: " + completionRes;
            throw new RetryException(errorMsg);
        }

        // 批改结果验证
        checkReviewed(areaObj, reviewed);
        areaRes.putAll(resolvedRes);
        areaRes.set("areaImg", areaObj.get("areaImg"));
    }


    @Override
    public void correctRecordAreaNormalQsTwoRequest(DocCorrectRecordDTO record,
                                                    JSONObject areaObj,
                                                    JSONObject areaRes,
                                                    GptAskLogEntity gptAskLogEntity) {
        JSONArray questions = areaObj.getJSONArray("questions");
        // 第一个请求
        JSONObject firstResolvedRes = correctRecordAreaNormalQsTheFirstRequest(record, areaObj, areaRes, gptAskLogEntity);
        // 第二个请求
        JSONArray firstReviewed = firstResolvedRes.getJSONArray("reviewed");
        JSONObject resolvedRes = correctRecordAreaNormalQsTheSecondRequest(record, areaObj, areaRes, gptAskLogEntity, firstReviewed);
        JSONArray reviewed = resolvedRes.getJSONArray("reviewed");
        if (reviewed.size() != questions.size()) {
            log.error("批改结果无法解析: {}, resolvedRes: {}", reviewed, resolvedRes);
            String errorMsg = "批改结果无法解析! reviewed: " + reviewed + ", resolvedRes: " + resolvedRes;
            throw new RetryException(errorMsg);
        }

        // 批改结果验证
        checkReviewed(areaObj, reviewed);
        areaRes.putAll(resolvedRes);
        areaRes.set("areaImg", areaObj.get("areaImg"));
    }

    private JSONObject correctRecordAreaNormalQsTheFirstRequest(DocCorrectRecordDTO record,
                                                                JSONObject areaObj,
                                                                JSONObject areaRes,
                                                                GptAskLogEntity gptAskLogEntity) {
        String aimodel = record.getAimodel();
        DocCorrectTaskDTO task = record.getTask();
        Boolean jsonschema = task.getJsonschema();
        CompletionCreator creator = getCreator(aimodel, jsonschema);
        gptAskLogEntity.setType(GptAskLogType.normalQsFirstQuest);
        // 1. 请求体创建阶段
        ChatCompletionDTO completion = null;
        try {
            completion = creator.createDocAreaNormalQsFirstRequest(record, areaObj, areaRes);

            completion.setTemperature(record.getTemperature()); // 设置模型 temperature 参数
            completion.setTopp(record.getTopp()); // 设置模型 top_p 参数
            if (Objects.isNull(completion.getTopp())) {
                completion.setTopp(DefaultCorrectConfigsConsts.topP);
            }
            completion.setSeed(DefaultCorrectConfigsConsts.seed);
            if (Objects.isNull(completion.getMaxTokens())) {
                completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
            }
            completion.setLogprobs(enableLogprobs);
            completion.setJsonSchema(record.getJsonschema());
            completion.setJsonObject(record.getJsonobject());
            completion.setModelRequestObj(record.getModelRequestContentObj());
        } catch (Exception e) {
            // 处理completion对象，去除base64图片
            Object completionObj = null;
            try {
                completionObj = completion;
                if (completionObj != null) {
                    com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(completionObj));
                    gptAskLogService.replaceBase64Images(json);
                    completionObj = json;
                }
            } catch (Exception ignore) {
            }
            log.error("请求体创建失败, completion对象: {}", completionObj, e);
            String errorMsg = "请求体创建失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", completion对象: " + completionObj;
            throw new RetryException(errorMsg, e);
        }

        areaRes.set("recordId", record.getId());

        // 2. 模型调用阶段
        Map<String, Object> completionRes = null;
        int qsCount = 1;
        try {
            qsCount = areaObj.getJSONArray("questions").size();
        } catch (Exception e) {
            log.error("计算错误qsCount: ", e);
        }
        try {
            completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, qsCount);
            areaRes.set("$response", completionRes.get("$response"));
        } catch (Exception e) {
            log.error("请求模型失败", e);
            // 模型调用失败
            String errorMsg = "模型调用失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            throw new RetryException(errorMsg, e);
        }

        // 3. 响应解析阶段
        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        JSONObject resolvedRes = null;
        try {
            resolvedRes = resolver.resolveDocAreaResponseFirstRequestRes(record, completionRes);
            // 批改结果 logprobs 获取
            if (enableLogprobs) {
                parseLogprobs(completionRes, resolvedRes);
            }
            return resolvedRes;
        } catch (Exception e) {
            log.error("批改结果解析失败: {}，解析对象: {}", resolvedRes, completionRes, e);
            // 响应解析失败
            String errorMsg = "响应解析失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", 解析对象: " + completionRes;
            throw new RetryException(errorMsg, e);
        }
    }


    private JSONObject correctRecordAreaNormalQsTheSecondRequest(DocCorrectRecordDTO record,
                                                           JSONObject areaObj,
                                                           JSONObject areaRes,
                                                           GptAskLogEntity gptAskLogEntity,
                                                           JSONArray reviewed) {
        String aimodel = record.getAimodel();
        DocCorrectTaskDTO task = record.getTask();
        Boolean jsonschema = task.getJsonschema();
        CompletionCreator creator = getCreator(aimodel, jsonschema);
        gptAskLogEntity.setType(GptAskLogType.normalQsSecondQuest);
        gptAskLogEntity.setId(null); // 清除ID，避免重复记录日志
        // 1. 请求体创建阶段
        ChatCompletionDTO completion = null;
        try {
            completion = creator.createDocAreaNormalQsSecondRequest(record, areaObj, areaRes, reviewed);

            completion.setTemperature(record.getTemperature()); // 设置模型 temperature 参数
            completion.setTopp(record.getTopp()); // 设置模型 top_p 参数
            if (Objects.isNull(completion.getTopp())) {
                completion.setTopp(DefaultCorrectConfigsConsts.topP);
            }
            completion.setSeed(DefaultCorrectConfigsConsts.seed);
            if (Objects.isNull(completion.getMaxTokens())) {
                completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
            }
            completion.setLogprobs(enableLogprobs);
            completion.setJsonSchema(record.getJsonschema());
            completion.setJsonObject(record.getJsonobject());
            completion.setModelRequestObj(record.getModelRequestContentObj());
        } catch (Exception e) {
            // 处理completion对象，去除base64图片
            Object completionObj = null;
            try {
                completionObj = completion;
                if (completionObj != null) {
                    com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(completionObj));
                    gptAskLogService.replaceBase64Images(json);
                    completionObj = json;
                }
            } catch (Exception ignore) {
            }
            log.error("请求体创建失败, completion对象: {}", completionObj, e);
            String errorMsg = "请求体创建失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", completion对象: " + completionObj;
            throw new RetryException(errorMsg, e);
        }

        areaRes.set("recordId", record.getId());

        // 2. 模型调用阶段
        Map<String, Object> completionRes = null;
        int qsCount = 1;
        try {
            qsCount = areaObj.getJSONArray("questions").size();
        } catch (Exception e) {
            log.error("计算错误qsCount: ", e);
        }
        try {
            completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, qsCount);
            areaRes.set("$response", completionRes.get("$response"));
        } catch (Exception e) {
            log.error("请求模型失败", e);
            // 模型调用失败
            String errorMsg = "模型调用失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            throw new RetryException(errorMsg, e);
        }

        // 3. 响应解析阶段
        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        JSONObject resolvedRes = null;
        try {
            resolvedRes = resolver.resolveDocAreaResponseFormatRes(record, completionRes);
            // 批改结果 logprobs 获取
            if (enableLogprobs) {
                parseLogprobs(completionRes, resolvedRes);
            }
            return resolvedRes;
        } catch (Exception e) {
            log.error("批改结果解析失败: {}，解析对象: {}", resolvedRes, completionRes, e);
            // 响应解析失败
            String errorMsg = "响应解析失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length()))) + ", 解析对象: " + completionRes;
            throw new RetryException(errorMsg, e);
        }
    }


    public JSONObject getChatCompletionDTO(String recordId, Integer areaIdx, String modal) {
        DocCorrectRecord record = docCorrectRecordMapper.selectById(recordId);
        DocCorrectRecordDTO recordDTO = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);

        recordDTO.setDocParh(recordDTO.getDocurl().substring(ctxPath.length() + 1));
        String taskId = record.getTaskId();
        DocCorrectTask docCorrectTask = docCorrectTaskMapper.selectById(taskId);
        Objects.requireNonNull(docCorrectTask, "未查询到任务信息");
        DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(docCorrectTask, DocCorrectTaskDTO.class);
        DocCorrectConfig config = docCorrectConfigMapper.selectById(docCorrectTask.getConfigId());
        Objects.requireNonNull(config, "未查询到配置信息");
        DocCorrectConfigDTO configDTO = BeanUtil.copyProperties(config, DocCorrectConfigDTO.class);

        recordDTO.setConfig(configDTO);
        recordDTO.setTask(taskDTO);

        // 批改之前，设置批改次数和批改模型
        beforeCorrect(recordDTO);
        JSONArray areasObj = FileUtil.INSTANCE.setDocAreasImg(recordDTO.getDocParh(), configDTO.getAreasObj());
        configDTO.setAreasObj(areasObj);

        DocCorrectConfigDTO docCorrectConfigDTO = recordDTO.getConfig();
        JSONArray areas = docCorrectConfigDTO.getAreasObj();
        JSONObject areaObj = areas.getJSONObject(areaIdx);

        JSONObject areaRes = new JSONObject();
        areaRes.set("status", "1"); // status(1：正常，2：异常)
        areaRes.set("areaIdx", areaIdx);
        try {
            boolean enabled = areaObj.getBool("enabled", true);
            if (!enabled) {
                return null;
            }
            Integer areaType = areaObj.getInt("areaType", 1);
            if (areaType == 1) {
                String aimodel = recordDTO.getAimodel();
                DocCorrectTaskDTO task = recordDTO.getTask();
                Boolean responseFormat = task.getResponseFormat();
                Boolean jsonschema = task.getJsonschema();
                CompletionCreator creator = getCreator(aimodel, jsonschema);
                ChatCompletionDTO completion = null;
                // ocr识别
                String ocrContent = "";
                try {
                    String service = "";
                    ocrContent = getOcrContent(service, areaObj.getStr("areaImg"), GetOcrContentType.CORRECT);
                } catch (InterruptedException e) {
                    log.error("ocr识别失败", e);
                    throw new RetryException("ocr识别失败!", e);
                }
                if (responseFormat) {
                    completion = creator.createDocAreaResponseFormatCompletion(recordDTO, areaObj, areaRes, ocrContent);
                } else {
                    completion = creator.createDocAreaCompletion(recordDTO, areaObj, areaRes);
                }
                completion.setTemperature(recordDTO.getTemperature()); // 设置模型 temperature 参数
                completion.setTopp(recordDTO.getTopp()); // 设置模型 top_p 参数
                completion.setLogprobs(enableLogprobs);
                if (Objects.isNull(completion.getTopp())) {
                    completion.setTopp(DefaultCorrectConfigsConsts.topP);
                }
                completion.setSeed(DefaultCorrectConfigsConsts.seed);
                if (Objects.isNull(completion.getMaxTokens())) {
                    completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
                }
                // 不同模型进行处理
                Map<String, Object> res = new HashMap<>();
                if (StrUtil.isNotBlank(modal)) {
                    res = getFinalParam(modal, completion);
                } else if (StrUtil.isNotBlank(aimodel)) {
                    res = getFinalParam(aimodel, completion);
                } else {
                    res = getFinalParam("gpt-4o-2024-08-06", completion);
                }
                return new JSONObject(res);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getOcrContent(String service, String areaImg, String type) throws InterruptedException {
        String ocrContent = "";
        // 假设返回多个服务实例，按优先级顺序排列
        List<OCRService> ocrServices = ocrServiceSelector.getOCRServiceChain(service, type);

        for (OCRService ocrService : ocrServices) {
            try {
                ocrContent = invokeOcrService(ocrService, areaImg);
                if (StrUtil.isNotBlank(ocrContent)) {
                    break;
                }
            } catch (Exception e) {
                log.error("OCR service :{} failed: :{}", ocrService.getClass().getSimpleName(), e.getMessage());
            }
        }

        // 若所有服务均无法获取有效结果，则返回提示信息
        if (StrUtil.isBlank(ocrContent)) {
            ocrContent = "";
        }
        return ocrContent;
    }

    private String invokeOcrService(OCRService ocrService, String areaImg) throws Exception {
        String result;
        if (Base64.isBase64(areaImg)) {
            result = ocrService.ocrForHandwritingText(areaImg);
        } else {
            String areaImgUrl = areaImg;
            if (!areaImg.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImg);
            }
            String base64 = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
            result = ocrService.ocrForHandwritingText(base64);
        }
        return result;
    }


    private String getArithmeticOcrContent(String service, String areaImg) throws InterruptedException {
        OCRService ocrService = ocrServiceSelector.getOCRService(service);
        String ocrContent = "";
        if (Base64.isBase64(areaImg)) {
            ocrContent = ocrService.ocrForHandwritingText(areaImg);
        } else {
            String areaImgUrl = areaImg;
            if (!areaImg.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImg);
            }
            String base64 = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
            ocrContent = ocrService.ocrForHandwritingText(base64);
        }
        if (StrUtil.isBlank(ocrContent)) {
            ocrContent = "图片中没有手写文字";
        }
        return ocrContent;
    }

    /**
     * 识别分数的批改
     *
     * @param record
     * @param areaObj
     * @param areaRes
     * @throws InterruptedException
     */
//    @Retryable(value = RetryException.class, maxAttempts = 0, listeners = {"docCorrectRetryListener"}, backoff = @Backoff(delay = 1000, multiplier = 2))
    @Override
    public void correctScoreArea(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity) throws InterruptedException {
        JSONArray questions = areaObj.getJSONArray("questions");
        Float score = 0F;
        if (!questions.isEmpty()) {
            JSONObject qs = questions.getJSONObject(0);
            score = qs.getFloat("score", 0F);
        }

        String aimodel = record.getAimodel();
        CompletionCreator creator = getCreator(aimodel, record.getJsonschema());
        // 首先使用ocr识别,默认使用腾讯
        String ocrContent = "";
        if (enableOcr && enableOcrForScore) {
            try {
                ocrContent = getArithmeticOcrContent("tencentOCRService", areaObj.getStr("areaImg"));
            } catch (Exception e) {
                log.error("ocr识别失败", e);
            }
            areaRes.set("ocrContent", ocrContent);
        }

        int qsCount = 1;
        try {
            qsCount = areaObj.getJSONArray("questions").size();
        } catch (Exception e) {
            log.error("计算错误qsCount: ", e);
        }
        // 其次使用大模型
        Map<String, Object> completionRes = new HashMap<>();
        try {
            ChatCompletionDTO completion = creator.createScoreCompletion(record, areaObj, areaRes, ocrContent);
            completion.setTemperature(record.getTemperature()); // 设置模型 temperature 参数
            completion.setModel(aimodel);
            completion.setJsonSchema(record.getJsonschema());
            completion.setJsonObject(record.getJsonobject());
            completion.setModelRequestObj(record.getModelRequestContentObj());

            completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, qsCount);
        } catch (Exception e) {
            throw new RetryException("请求模型失败!", e);
        }

        JSONObject resolvedRes = null;
        try {
            CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
            resolvedRes = resolver.resolveScoreRes(record, completionRes, score);
        } catch (Exception e) {
            throw new RetryException("批改结果解析失败!", e);
        }
        areaRes.putAll(resolvedRes);
        areaRes.set("areaImg", areaObj.get("areaImg"));
    }

    private void checkReviewed(JSONObject areaObj, JSONArray reviewed) {
        // 分数校验，最大分数为题目分数
        JSONArray questions = areaObj.getJSONArray("questions");
        for (int i = 0; i < reviewed.size(); i++) {
            JSONObject qsObj = questions.getJSONObject(i);
            JSONObject review = reviewed.getJSONObject(i);
            if (Objects.nonNull(review.get("scored"))) {
                review.putByPath(String.format("[%s].scored", i),
                        NumberUtil.min(qsObj.getBigDecimal("score", BigDecimal.ZERO), review.getBigDecimal("scored", BigDecimal.ZERO)));
            }
        }

        RetryContext context = RetrySynchronizationManager.getContext();
        if (context != null) {
            int retryCount = context.getRetryCount();
            log.info("retryCount={}", retryCount);
            if (retryCount > 3) {
                return; // 重试次数过多,不再校验
            }
        }

        for (int i = 0; i < reviewed.size(); i++) {
            Integer opinion = areaObj.getByPath(String.format("questions[%s].opinion", i), Integer.class);
            opinion = Optional.ofNullable(opinion).orElse(2); // 默认主观,不需要校验
            if (opinion != 1) {
                continue;
            }
            JSONObject qsRes = reviewed.getJSONObject(i);
            boolean isTrue = qsRes.getStr("isCorrect").equals("Y");
            String studentAnswer = qsRes.getStr("studentAnswer", "");
            String correctAnswer = areaObj.getByPath(String.format("questions[%s].answer", i), String.class);
            boolean actTrue = Objects.equals(correctAnswer.trim(), studentAnswer.trim());
//            if (isTrue != actTrue) {
//                log.error("批改结果验证失败: qsIdx={}, studentAnswer={}, correctAnswer={}, actTrue={}", i, studentAnswer, correctAnswer, actTrue);
//                throw new RetryException("批改结果验证失败!");
//            }
            if (actTrue && !isTrue) {
                qsRes.set("isCorrect", "Y");
                qsRes.set("scored", areaObj.getByPath(String.format("questions[%s].score", i)));
                reviewed.set(i, qsRes);
            }

        }
    }

    /**
     * 如果 json_schmea : true 一定走 VisionCompletionCreator（专门处理json_schema的）
     * 其他的是 DouBaoCompletionCreator（直接输出text）
     *
     * @param aimodel
     * @param jsonSchema
     * @return
     */
    public CompletionCreator getCreator(String aimodel, Boolean jsonSchema) {
        for (CompletionCreator creator : creators) {
            if (creator.isSupported(aimodel, jsonSchema)) {
                return creator;
            }
        }
        return null;
    }

    public BasicAiService getService(String aimodel) {
        for (BasicAiService service : services) {
            if (service.isSupport(aimodel)) {
                return service;
            }
        }
        return null;
    }

    public CompletionResResolver getResover(String aimodel, Boolean jsonSchema) {
        for (CompletionResResolver resolver : resolvers) {
            if (resolver.isSupported(aimodel, jsonSchema)) {
                return resolver;
            }
        }
        return null;
    }

    //    @Retryable(value = RetryException.class, maxAttempts = 0)
    @Override
    public List<CorrectQsDTO> extraQs(ExtraQsForm params) {
        String aimodel = params.getAimodel();
        if (Objects.isNull(aimodel)) {
            aimodel = defaultModelRedisService.getModelValue();
        }
        CompletionCreator creator = getCreator(aimodel, defaultModelRedisService.getJsonschema());

        ChatCompletionDTO completion = creator.createRxtraQsCompletion(params);
        completion.setJsonSchema(defaultModelRedisService.getJsonschema());
        completion.setJsonObject(defaultModelRedisService.getJsonobject());
        completion.setModelRequestObj(defaultModelRedisService.getContentObj());
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setType(GptAskLogType.extraQs);
        gptAskLogEntity.setTaskId("extraQs");
        // 将图片保存下来
        try {
            gptAskLogEntity.setImgUrl(FileUtil.INSTANCE.saveBase64Image(params.getImgStr()));
        } catch (Exception e) {
            log.error("图片保存失败: {} ", e.getMessage());
        }

        gptAskLogEntity.setImgUrl(params.getImgStr());
        Map<String, Object> completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, 1);

        CompletionResResolver resolver = getResover(aimodel, defaultModelRedisService.getJsonschema());
        //
        List<CorrectQsDTO> resolvedRes = null;
        try {
            // resolvedRes = resolver.resolveRxtraQsRes(completionRes);
            resolvedRes = resolver.resolveExtraQsResWithRespFormat(completionRes);
        } catch (Exception e) {
            log.error("题目识别失败", e);
            throw new RetryException("题目识别失败!", e);
        }

        if (Objects.nonNull(resolvedRes) && !resolvedRes.isEmpty()) {
            for (CorrectQsDTO qs : resolvedRes) {
                if (StrUtil.isBlank(qs.getAnswer()) && StrUtil.isNotBlank(qs.getQuestion())) {
                    qs.setAnswer(qs.getQuestion());
                }
            }
        }
        return resolvedRes;
    }

    private Map<String, Object> chatCompletion(String aimodel, ChatCompletionDTO completion, GptAskLogEntity gptAskLogEntity, Integer qsCount) {
        BasicAiService service = getService(aimodel);
        Map<String, Object> completionRes = null;
        try {
            if (StrUtil.isNotBlank(gptAskLogEntity.getTaskId())) {
                correctCacheService.onRpmRecorded(gptAskLogEntity.getTaskId(), qsCount);
            }
        } catch (Exception e) {
            log.error("记录失败 Exception：", e);
        }

        try {
            completionRes = service.chatForCompletion(completion, gptAskLogEntity);
        } catch (RetryException e) {
            // 直接抛出，让上层处理
            throw e;
        } catch (Exception e) {
            log.error("请求模型失败", e);
            // 模型调用失败
            String errorMsg = "模型调用失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            throw new RetryException(errorMsg, e);
        }
        return completionRes;
    }

    private Map<String, Object> getFinalParam(String aimodel, ChatCompletionDTO completion) {
        BasicAiService service = getService(aimodel);
        Map<String, Object> completionRes = null;
        if (service == null) {
            log.info("找不到对应的service");
            return null;
        }
        try {
            completionRes = service.getFinalCompletion(completion);
        } catch (RetryException e) {
            throw e;
        } catch (Exception e) {
            log.error("请求模型失败", e);
            String errorMsg = "请求模型失败: " + (e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            throw new RetryException(errorMsg, e);
        }
        return completionRes;
    }

    @Override
    public void correctAnswerCard(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity) {
        JSONArray questions = areaObj.getJSONArray("questions");
        JSONArray reviewed = new JSONArray();
        for (int qsIdx = 0; qsIdx < questions.size(); qsIdx++) {
            JSONObject qs = questions.getJSONObject(qsIdx);
            JSONObject reviewedQs = answerCardQsReview(record, areaRes.getInt("areaIdx"), qsIdx, qs);
            reviewed.add(reviewedQs);
        }
        areaRes.set("reviewed", reviewed);
    }

//    public JSONObject answerCardQsReview(DocCorrectRecordDTO record, Integer areaIdx, Integer qsIdx, JSONObject qs) {
//        JSONObject optionArea = qs.getJSONObject("optionArea");
//        Integer offsetX = Objects.isNull(record.getOffsetX()) ? 0 : record.getOffsetX();
//        Integer offsetY = Objects.isNull(record.getOffsetY()) ? 0 : record.getOffsetY();
//        String qsOptionImg = FileUtil.INSTANCE.docAreaImg(record.getDocParh(), optionArea.getInt("x") + offsetX, optionArea.getInt("y") + offsetY,
//                optionArea.getInt("width"), optionArea.getInt("height"));
//        log.info("answer card review: {} {} {} {} {}", record.getId(), record.getDocname(), areaIdx, qsIdx, qsOptionImg);
//        JSONObject reviewRes = answerCardReviewService.reviewAnswerCard(FileUtil.INSTANCE.absPath(qsOptionImg), qs);
//
//        JSONArray studentAnswer = reviewRes.getJSONArray("studentAnswer");
//        JSONArray answer = qs.getJSONArray("answer");
//        String isCorrect = "N";
//        if (answer.size() == studentAnswer.size()) {
//            Set<Integer> stuAnswer = new HashSet<>(studentAnswer.toList(Integer.class));
//            Set<Integer> corAnswer = new HashSet<>(answer.toList(Integer.class));
//            if (stuAnswer.equals(corAnswer)) {
//                isCorrect = "Y";
//            }
//        }
//        reviewRes.set("isCorrect", isCorrect);
//        return reviewRes;
//    }

    public JSONObject answerCardQsReview(DocCorrectRecordDTO record, Integer areaIdx, Integer qsIdx, JSONObject qs) {
        // 1. 检查输入参数是否为 null
        if (record == null || qs == null) {
            throw new IllegalArgumentException("Record or QS JSONObject cannot be null");
        }

        JSONObject optionArea = qs.getJSONObject("optionArea");
        // 2. 检查 optionArea 是否为 null 或缺少必要字段
        if (optionArea == null || !optionArea.containsKey("x") || !optionArea.containsKey("y")
                || !optionArea.containsKey("width") || !optionArea.containsKey("height")) {
            throw new IllegalArgumentException("Invalid optionArea: missing required fields (x, y, width, height)");
        }

        // 3. 处理可能的拼写错误
        String docPath = record.getDocParh(); // 或 record.getDocPath()?
        if (docPath == null || docPath.trim().isEmpty()) {
            throw new IllegalArgumentException("Document path cannot be null or empty");
        }

        // 4. 安全获取偏移量
        Integer offsetX = Objects.isNull(record.getOffsetX()) ? 0 : record.getOffsetX();
        Integer offsetY = Objects.isNull(record.getOffsetY()) ? 0 : record.getOffsetY();

        // 5. 安全调用 docAreaImg
        String qsOptionImg;
        try {
            qsOptionImg = FileUtil.INSTANCE.docAreaImg(
                    docPath,
                    optionArea.getInt("x") + offsetX,
                    optionArea.getInt("y") + offsetY,
                    optionArea.getInt("width"),
                    optionArea.getInt("height")
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate document area image", e);
        }

        log.info("answer card review: {} {} {} {} {}", record.getId(), record.getDocname(), areaIdx, qsIdx, qsOptionImg);
        JSONObject reviewRes = answerCardReviewService.reviewAnswerCard(FileUtil.INSTANCE.absPath(qsOptionImg), qs);

        JSONArray studentAnswer = reviewRes.getJSONArray("studentAnswer");
        JSONArray answer = qs.getJSONArray("answer");
        String isCorrect = "N";
        if (answer.size() == studentAnswer.size()) {
            Set<Integer> stuAnswer = new HashSet<>(studentAnswer.toList(Integer.class));
            Set<Integer> corAnswer = new HashSet<>(answer.toList(Integer.class));
            if (stuAnswer.equals(corAnswer)) {
                isCorrect = "Y";
            }
        }
        reviewRes.set("isCorrect", isCorrect);
        return reviewRes;
    }

    @Override
    public Map<String, Object> correctWriteQs(String aimodel, ChatCompletionDTO completion, String taskId, GptAskLogEntity gptAskLogEntity) {
        if (Objects.isNull(completion.getMaxTokens())) {
            completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        }
        completion.setTopp(DefaultCorrectConfigsConsts.topP);
        completion.setSeed(DefaultCorrectConfigsConsts.seed);
        completion.setJsonSchema(defaultModelRedisService.getJsonschema());
        completion.setJsonObject(defaultModelRedisService.getJsonobject());

        return chatCompletion(aimodel, completion, gptAskLogEntity, 1);
    }

    //    @Retryable(value = RetryException.class, maxAttempts = 0, listeners = {"docCorrectRetryListener"}, backoff = @Backoff(delay = 1000, multiplier = 2))
    @Override
    public void correctWriteQsTwiceMergers(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity) throws InterruptedException {
        // 作文批改无法使用3倍批改
        if (StrUtil.isBlank(record.getAimodel()) || record.getAimodel().equals(AIModelConsts.GPT_4O_20240806_3)) {
            record.setAimodel(AIModelConsts.GPT_4O_20240806);
        }
        if (StrUtil.isBlank(record.getTask().getAimodel()) || record.getTask().getAimodel().equals(AIModelConsts.GPT_4O_20240806_3)) {
            DocCorrectTaskDTO taskDTO = record.getTask();
            taskDTO.setAimodel(AIModelConsts.GPT_4O_20240806);
            record.setTask(taskDTO);
        }
        JSONArray questions = areaObj.getJSONArray("questions");
        JSONObject essayConfigObj = null;
        if (!questions.isEmpty()) {
            essayConfigObj = questions.getJSONObject(0).getJSONObject("qsInfo");
        }
        Integer allScore = Integer.valueOf(questions.getJSONObject(0).getStr("score", "1"));
        Boolean isEnglishEssay = true;
        try {
            isEnglishEssay = essayConfigObj.getBool("isEnglishEssay");
            if (isEnglishEssay == null) {
                isEnglishEssay = true;
            }
        } catch (Exception e) {
            isEnglishEssay = true;
            log.error("cannot get isEnglishEssay DocCorrectRecordDTO:{}", record);
            throw new RetryException("cannot get isEnglishEssay DocCorrectRecordDTO!", e);
        }
        // 第0次请求，调用腾讯云文字识别
        // 后面可以再配置里设置，现在默认腾讯云
        String service = "";
        String areaImg = areaObj.getStr("areaImg");
        String ocrContent = "";
        if (enableOcr && enableOcrForEssay) {
            ocrContent = getOcrContent(service, areaImg, GetOcrContentType.ESSAY);
        }
        JSONObject huaShiApiResult = null;
        String aimodel = record.getAimodel();

        if (!isEnglishEssay) {
            huaShiApiResult = huaShiApi.request(getBase64WithoutImageType(areaImg));
            CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
            JSONObject resolvedRes = resolver.resolveDocWriteQsHuaShiRes(record, allScore, huaShiApiResult);
            JSONArray reviewed = resolvedRes.getJSONArray("reviewed");
            checkReviewed(areaObj, reviewed);
            areaRes.putAll(resolvedRes);
            return;
        }
        // 第一次请求

        CompletionCreator creator = getCreator(aimodel, record.getJsonschema());
        Map<String, Object> completionRes = new HashMap<>();
        String essay = "";
        try {
            ChatCompletionDTO completions1Request = creator.createWriteQsCompletion1Request(record, areaObj, areaRes, allScore, ocrContent, record.getModelRequestId());
            correctWriteQs(aimodel, completions1Request, record.getTaskId(), gptAskLogEntity);
            essay = resolveDocWriteQsEssay(completionRes);
        } catch (Exception e) {
            throw new RetryException("批改结果无法解析!");
        }


        String gradeName = "初一";
        String title = "";
        try {
            if (essayConfigObj != null) {
                gradeName = essayConfigObj.getStr("grade");
            }
            if (StrUtil.isBlank(gradeName)) {
                gradeName = "初一";
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
        }
        try {
            if (essayConfigObj != null) {
                title = essayConfigObj.getStr("essayTitle");
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
        }
        // 第二次请求- 根据是语文还是英语作文 使用 腾讯或者有道云的api接口 或 华师作文批改语文
        CorrectData tencentCorrectData = null;
        ECCRequest req = new ECCRequest();
        String essayContent = extractEssayContentFromCompletionResRequest(completionRes);
        req.setContent(essayContent);  // 作文内容
        req.setTitle(title);  // 可选作文题目
        String grade = convertLabelToGrade(gradeName);
        req.setGrade(grade);  // 年级
        String requirement = DefaultCorrectConfigsConsts.essayDefaultRequirement;
        try {
            if (essayConfigObj != null) {
                String materials = essayConfigObj.getStr("materials");
                String score = essayConfigObj.getStr("score");
                if (StrUtil.isNotBlank(materials) && StrUtil.isNotBlank(score)) {
                    requirement = String.format(DefaultCorrectConfigsConsts.essayRequirement, score, materials);
                }
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
            throw new RetryException("获取班级名称失败", e);
        }
        req.setRequirement(requirement);  // 作文要求
        tencentCorrectData = tencentEssayScoreService.correctEssay(req);


        // 第三次请求
        Map<String, Object> completionRes2Request = new HashMap<>();
        try {
            String correctDataStr = JSONUtil.toJsonStr(isEnglishEssay ? tencentCorrectData : huaShiApiResult);
            ChatCompletionDTO completions2Request = creator.createWriteQsCompletion2Request(record, areaObj, essay, gradeName, title, correctDataStr, allScore, isEnglishEssay, record.getModelRequestId());
            completionRes2Request = correctWriteQs(aimodel, completions2Request, record.getTaskId(), gptAskLogEntity);
        } catch (Exception e) {
            throw new RetryException("请求模型失败!", e);
        }

        // 第四次请求，作文重新 分数tencent评估
        CorrectData tencentCorrectDataRewrite = null;
        JSONObject huaShiApiResultRewrite = null;
        String essayContentRewrite = extractEssayContentRewriteFromCompletionResRequest(completionRes2Request);
        ECCRequest reqRewrite = new ECCRequest();
        reqRewrite.setContent(essayContentRewrite);  // 作文内容
        reqRewrite.setTitle(title);  // 可选作文题目
        reqRewrite.setGrade(grade);  // 年级
        reqRewrite.setRequirement(requirement);  // 作文要求
        try {
            tencentCorrectDataRewrite = tencentEssayScoreService.correctEssay(reqRewrite);
        } catch (Exception e) {
            throw new RetryException("腾讯批改失败", e);
        }

        // 合并
        String rawResp1Request = (String) completionRes.get("$response");
        JSONObject response1Request = JSONUtil.parseObj(rawResp1Request);
        String rawResp2Request = (String) completionRes2Request.get("$response");
        JSONObject response2Request = JSONUtil.parseObj(rawResp2Request);
        response1Request.putAll(response2Request);
        completionRes.put("$response", JSONUtil.toJsonStr(response1Request));

        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        JSONObject resolvedRes = null;
        try {
            resolvedRes = resolver.resolveDocWriteQsRes(record, completionRes, allScore, tencentCorrectData, tencentCorrectDataRewrite);
        } catch (Exception e) {
            log.error("批改结果解析失败: {}", resolvedRes, e);
            throw new RetryException("批改结果解析失败!", e);
        }
        JSONArray reviewed = resolvedRes.getJSONArray("reviewed");
        if (reviewed.size() != questions.size()) {
            log.error("批改结果无法解析: {}, completionRes: {}", reviewed, completionRes);
            String errorMsg = "批改结果无法解析! reviewed: " + reviewed + ", completionRes: " + completionRes;
            throw new RetryException(errorMsg);
        }
        // 批改结果验证
        checkReviewed(areaObj, reviewed);

        areaRes.putAll(resolvedRes);
    }

    private String getBase64WithoutImageType(String areaImg) throws InterruptedException {
        String areaImgUrl = areaImg;
        if (!areaImg.startsWith("http")) {
            areaImgUrl = String.format("%s%s", serverUrl, areaImg);
        }
        return FileUtil.INSTANCE.url2Base64(areaImgUrl);
    }

    /**
     * 将给定的 label 转换为对应的 grade
     *
     * @param label 年级的描述标签
     * @return 对应的年级等级，如果没有匹配项则返回 null
     */
    public static String convertLabelToGrade(String label) {
        if (StrUtil.isBlank(label)) {
            return "elementary";
        }
        // 查找并返回对应的年级等级
        return tencentGradeMap.get(label);
    }

    private String extractEssayContentFromCompletionResRequest(Map<String, Object> responseMap) {
        String responseStr = (String) responseMap.get("$response");
        JSONObject content = JSONUtil.parseObj(responseStr);
        return content.getStr("学生作文全文");
    }

    private String extractEssayContentRewriteFromCompletionResRequest(Map<String, Object> responseMap) {
        String responseStr = (String) responseMap.get("$response");
        JSONObject content = JSONUtil.parseObj(responseStr);
        return content.getStr("作文重写");
    }

    //    @Retryable(value = RetryException.class, maxAttempts = 0, listeners = {"docCorrectRetryListener"})
    @Override
    public String createEssayAnalyticalReportCompletion(List<DocCorrectRecordDTO> records, DocCorrectRecordDTO record, String scoreSituation, String gradeName) {
        // 作文批改无法使用3倍批改
        if (StrUtil.isBlank(record.getAimodel()) || record.getAimodel().equals(AIModelConsts.GPT_4O_20240806_3)) {
            record.setAimodel(AIModelConsts.doubao_15_pro);
        }
        if (record.getTask() == null || StrUtil.isBlank(record.getTask().getAimodel()) || record.getTask().getAimodel().equals(AIModelConsts.GPT_4O_20240806_3)) {
            DocCorrectTaskDTO taskDTO = record.getTask();
            if (taskDTO == null) {
                taskDTO = new DocCorrectTaskDTO();
            }
            taskDTO.setAimodel(AIModelConsts.doubao_15_pro);
            record.setTask(taskDTO);
        }

        String aimodel = record.getAimodel();
        CompletionCreator creator = getCreator(aimodel, record.getJsonschema());
        ChatCompletionDTO completionsRequest = creator.createEssayAnalyticalReportCompletion(records, record, scoreSituation, gradeName);
        completionsRequest.setModel(aimodel);

        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setRecordId(record.getId());
        gptAskLogEntity.setTaskId(record.getTaskId());
        gptAskLogEntity.setConfigId(record.getConfigId());
        gptAskLogEntity.setAimodel(aimodel);
        gptAskLogEntity.setFileId(record.getTask().getFileId());
        gptAskLogEntity.setType(GptAskLogType.essayAnalyticalReport);
        gptAskLogEntity.setRecordName(record.getDocname());
        Map<String, Object> completionRes = correctWriteQs(aimodel, completionsRequest, record.getTaskId(), gptAskLogEntity);

        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        String resolvedRes = "";
        try {
            resolvedRes = resolver.resolveEssayAnalyticalReportDocRes(completionRes);
        } catch (Exception e) {
            log.error("作文分析结果解析失败: {}", resolvedRes, e);
            throw new RetryException("作文分析结果解析失败!", e);
        }
        return resolvedRes;
    }

    private String resolveDocWriteQsEssay(Map<String, Object> completionRes) {
        log.info("resolveDocWriteQsEssay: {}", completionRes);
        String rawResp = (String) completionRes.get("$response");
        JSONObject content = JSONUtil.parseObj(rawResp);
        return content.getStr("学生作文全文");
    }

    //    @Retryable(value = RetryException.class, maxAttempts = 0)
    @Override
    public OrgCorrectResult correctOrgQs(OrgQuestionDTO params) {
        String aimodel = AIModelConsts.GPT_4O_MINI;
        boolean responseFormat = false;
        float temperature = 0f;

        DocCorrectRecordDTO record = new DocCorrectRecordDTO();
        record.setAimodel(aimodel);
        JSONObject areaObj = new JSONObject();
        JSONArray questions = new JSONArray();
        questions.add(new JSONObject()
                .set("question", params.getQuestion())
                .set("qsInfo", params.getQuestionInfo())
                .set("answer", params.getCorrectAnswer())
                .set("score", params.getScore()));
        areaObj.set("questions", questions);
        JSONObject areaRes = new JSONObject();
        areaRes.set("areaImg", params.getCorrectImage());
        // ocr识别
        String ocrContent = "";
        try {
            String service = "";
            ocrContent = getOcrContent(service, areaObj.getStr("areaImg"), GetOcrContentType.CORRECT);
        } catch (InterruptedException e) {
            log.error("ocr识别失败", e);
            throw new RetryException("ocr识别失败!", e);
        }
        CompletionCreator creator = getCreator(aimodel, record.getJsonschema());
        ChatCompletionDTO completion = null;
        if (responseFormat) {
            completion = creator.createDocAreaResponseFormatCompletion(record, areaObj, areaRes, ocrContent);
        } else {
            completion = creator.createDocAreaCompletion(record, areaObj, areaRes);
        }
        completion.setTemperature(temperature);
        completion.setJsonSchema(record.getJsonschema());
        completion.setJsonObject(record.getJsonobject());
        int qsCount = 1;
        try {
            qsCount = areaObj.getJSONArray("questions").size();
        } catch (Exception e) {
            log.error("计算错误qsCount: ", e);
        }
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setRecordId(record.getId());
        gptAskLogEntity.setTaskId(record.getTaskId());
        gptAskLogEntity.setAimodel(aimodel);
        gptAskLogEntity.setFileId(record.getTask().getFileId());
        gptAskLogEntity.setType(GptAskLogType.correctOrgQs);
        Map<String, Object> completionRes = chatCompletion(aimodel, completion, gptAskLogEntity, qsCount);

        CompletionResResolver resolver = getResover(aimodel, record.getJsonschema());
        JSONObject resolvedRes = null;
        try {
            if (responseFormat) {
                resolvedRes = resolver.resolveDocAreaResponseFormatRes(record, completionRes);
            } else {
                resolvedRes = resolver.resolveDocAreaRes(record, completionRes);
            }
        } catch (Exception e) {
            log.error("批改结果解析失败: {}", resolvedRes, e);
            throw new RetryException("批改结果解析失败!", e);
        }

        JSONArray reviewed = resolvedRes.getJSONArray("reviewed");
        if (Objects.isNull(reviewed) || reviewed.isEmpty()) {
            throw new RetryException("批改结果解析失败!");
        }
        JSONObject reviewRes = reviewed.getJSONObject(0);
        OrgCorrectResult result = new OrgCorrectResult();
        result.setStudentAnswer(reviewRes.getStr("studentAnswer"));
        result.setCorrect(Objects.equals(reviewRes.getStr("isCorrect", "Y"), "Y"));
        result.setScore(reviewRes.getBigDecimal("scored", BigDecimal.ZERO));
        result.setComment(reviewRes.getStr("review"));

        return result;
    }

    /**
     * logprobs 结果解析，获取所有标记批改是否正确的token的logprobs
     */
    private void parseLogprobs(Map<String, Object> completionRes, JSONObject reviewedRes) {
        log.info("解析logprobs: {}", completionRes.get("logprobs"));
        JSONArray logprobs = Convert.convert(JSONArray.class, completionRes.get("$logprobs"));
        if (Objects.isNull(logprobs)) {
            return;
        }
        // 找到所有token为true或false的logprob,并判断上一个字符是否是':'
        List<JSONObject> logprobsRes = new ArrayList<>();
        JSONObject item = null;
        String lastToken = null;
        for (int i = 0; i < logprobs.size(); i++) {
            item = logprobs.getJSONObject(i);
            String token = item.getStr("token");
            if (Objects.equals(token, "true") || Objects.equals(token, "false")) {
                if (Objects.nonNull(lastToken) && lastToken.endsWith(":")) {
                    logprobsRes.add(item);
                }
            }
            lastToken = token;
        }
        log.info("解析logprobs结果: {}", logprobsRes);
        // 将解析的logprobs添加到reviewedRes
        JSONArray reviewed = reviewedRes.getJSONArray("reviewed");
        for (int i = 0; i < reviewed.size(); i++) {
            JSONObject res = reviewed.getJSONObject(i);
            if (i < logprobsRes.size()) {
                res.set("logprob", logprobsRes.get(i));
            }
            reviewed.set(i, res);
        }
        reviewedRes.set("reviewed", reviewed);
    }

    @Override
    public String extraStudentName(String imgUrl,
                                   String ocrText,
                                   String aimodel,
                                   GptAskLogEntity gptAskLogEntity,
                                   Integer modelRequestId) {
        if (StrUtil.isBlank(aimodel)) {
            aimodel = defaultModelRedisService.getModelValue();
        }
        CompletionCreator creator = getCreator(aimodel, CorrectConfigConsts.defaultJsonSchemaValue);
        ChatCompletionDTO completionsRequest = creator.createExtractStudentName(aimodel, imgUrl, ocrText, modelRequestId);
        completionsRequest.setJsonSchema(defaultModelRedisService.getJsonschema());
        completionsRequest.setJsonObject(defaultModelRedisService.getJsonobject());
        // 提取
        Map<String, Object> completionRes = chatCompletion(aimodel, completionsRequest, gptAskLogEntity, 1);
        log.info("extraStudentName completionRes: {}", completionRes);
        CompletionResResolver resolver = getResover(aimodel, CorrectConfigConsts.defaultJsonSchemaValue);
        String name = null;
        try {
            name = resolver.resolveStudentNameRes(completionRes);
            return name;
        } catch (Exception e) {
            log.error("extraStudentName error :{}", e.getMessage());
            name = "提取姓名报错-llm";
        }
        return name;
    }

    @Override
    public String extraStudentNumber(String imgUrl,
                                     String ocrText,
                                     String aimodel,
                                     GptAskLogEntity gptAskLogEntity,
                                     Integer modelRequestId) {
        if (StrUtil.isBlank(aimodel)) {
            aimodel = defaultModelRedisService.getModelValue();
        }
        CompletionCreator creator = getCreator(aimodel, CorrectConfigConsts.defaultJsonSchemaValue);
        ChatCompletionDTO completionsRequest = creator.createExtractStudentNumber(aimodel, imgUrl, ocrText, modelRequestId);
        completionsRequest.setJsonSchema(defaultModelRedisService.getJsonschema());
        completionsRequest.setJsonObject(defaultModelRedisService.getJsonobject());
        // 提取
        Map<String, Object> completionRes = chatCompletion(aimodel, completionsRequest, gptAskLogEntity, 1);
        log.info("extraStudentNumber completionRes: {}", completionRes);
        CompletionResResolver resolver = getResover(aimodel, CorrectConfigConsts.defaultJsonSchemaValue);
        String studentNumber = null;
        try {
            studentNumber = resolver.resolveStudentNumberRes(completionRes);
            return studentNumber;
        } catch (Exception e) {
            log.error("extraStudentNumber error :{}", e.getMessage());
            studentNumber = "提取学号报错-llm";
        }
        return studentNumber;
    }

    @Override
    public PaperTopicDTO extraPaperTopic(List<String> imgUrls, List<PaperTopicDTO> topics, String aimodel, GptAskLogEntity gptAskLogEntity) {
        if (StrUtil.isBlank(aimodel)) {
            aimodel = defaultModelRedisService.getModelValue();
        }
        gptAskLogEntity.setType(GptAskLogType.standardVolumeMatching);
        CompletionCreator creator = getCreator(aimodel, defaultModelRedisService.getJsonschema());
        ChatCompletionDTO completionsRequest = creator.createExtractPaperTopic(aimodel, imgUrls, topics);
        completionsRequest.setJsonSchema(defaultModelRedisService.getJsonschema());
        completionsRequest.setJsonObject(defaultModelRedisService.getJsonobject());
        completionsRequest.setModelRequestObj(defaultModelRedisService.getContentObj());
        // 提取
        Map<String, Object> completionRes = chatCompletion(aimodel, completionsRequest, gptAskLogEntity, 1);
        CompletionResResolver resolver = getResover(aimodel, defaultModelRedisService.getJsonschema());
        PaperTopicDTO paperTopicDTO = null;
        try {
            paperTopicDTO = resolver.resolvePaperTopicRes(completionRes);
            return paperTopicDTO;
        } catch (Exception e) {
            log.error("extraTopic error :{}", e.getMessage());
            paperTopicDTO = null;
        }
        return paperTopicDTO;
    }
}

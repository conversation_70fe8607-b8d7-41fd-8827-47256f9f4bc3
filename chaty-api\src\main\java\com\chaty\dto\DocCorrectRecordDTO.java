package com.chaty.dto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.ModelRequest;
import com.chaty.enums.CorrectConfigConsts;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.exception.BaseException;
import freemarker.ext.beans.HashAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.chaty.enums.CorrectConfigConsts.*;

@Data
public class DocCorrectRecordDTO extends DocCorrectRecord {

    private static final Logger log = LoggerFactory.getLogger(DocCorrectRecordDTO.class);
    private PageDTO<DocCorrectRecord> page;

    private String docParh;

    private DocCorrectTaskDTO task;

    private DocCorrectConfigDTO config;

    private Boolean isPreview; // 是否预览

    private Boolean isBatch; // 是否批量

    private Integer areaNum;

    private Integer areaCorrected;

    private Boolean showQsScore = false;

    private String taskName;

    private String[] tagIds;

    private String aimodel;

    private Integer correctTimes;

    private Float temperature;

    private Float topp;

    private SaveRemoteFileDTO remoteFileProps;

    private PrinterPropsDTO printerProps;

    private String tenantId;

    private Boolean enableLevelOutput;

    private String levelRangeTxt;

    private JSONObject papersScore;

    private LevelRangeObj levelRangeObj;

    private String totalScoreAreaX;

    private String totalScoreAreaY;

    private JSONObject totalScoreArea;

    private JSONObject reviewedObj;

    // 按照percentage排序,从小到大
    private JSONObject ranges;

    /**
     * 导出的时候是否需要旋转
     */
    private Boolean isRotate = false;

    private Boolean isOld = false;

    private Boolean orderByTaskId;

    // 是否是第一页,第一页需要展示分数
    private Boolean isFirstPage = false;

    private String firstPageScore = "";

    private String scoreColor = "red";

    private JSONObject modelRequestContentObj;

    private Integer modelRequestId;

    private ModelRequest modelRequest;


    public JSONArray getReviewedObj() {
        return JSONUtil.parseArray(getReviewed());
    }

    public JSONObject getJSONObj(String jsonStr) {
        return JSONUtil.parseObj(jsonStr);
    }

    public JSONObject mergeScore(JSONObject score1, JSONObject score2) {
        if (Objects.isNull(score1)) {
            return score2;
        }
        if (Objects.isNull(score2)) {
            return score1;
        }
        // 提取score1和score2中的所有需要合并的字段
        BigDecimal totalScore1 = score1.getBigDecimal("totalScore", BigDecimal.ZERO);
        BigDecimal scored1 = score1.getBigDecimal("scored", BigDecimal.ZERO);
        BigDecimal additionalScore1 = score1.getBigDecimal("additionalScore", BigDecimal.ZERO);
        BigDecimal additionalScored1 = score1.getBigDecimal("additionalScored", BigDecimal.ZERO);
        JSONObject scoreTypeMap1 = score1.getJSONObject("scoreTypeMap");
        JSONObject scoreTypeMap2 = score2.getJSONObject("scoreTypeMap");
        if (scoreTypeMap1 == null) {
            scoreTypeMap1 = new JSONObject();
        }
        if (scoreTypeMap2 == null) {
            scoreTypeMap2 = new JSONObject();
        }


        BigDecimal totalScore2 = score2.getBigDecimal("totalScore", BigDecimal.ZERO);
        BigDecimal scored2 = score2.getBigDecimal("scored", BigDecimal.ZERO);
        BigDecimal additionalScore2 = score2.getBigDecimal("additionalScore", BigDecimal.ZERO);
        BigDecimal additionalScored2 = score2.getBigDecimal("additionalScored", BigDecimal.ZERO);

        // 合并总分、已得分、附加分
        BigDecimal totalScore = totalScore1.add(totalScore2);
        BigDecimal scored = scored1.add(scored2);
        BigDecimal additionalScore = additionalScore1.add(additionalScore2);
        BigDecimal additionalScored = additionalScored1.add(additionalScored2);

        // 合并scoreTypeMap
        for (Map.Entry<String, Object> entry : scoreTypeMap2.entrySet()) {
            String type = entry.getKey();
            JSONObject scoreTypeObj2 = (JSONObject) entry.getValue();

            // 如果scoreTypeMap1中没有此类型，则直接加入
            if (!scoreTypeMap1.containsKey(type)) {
                scoreTypeMap1.put(type, scoreTypeObj2);
            } else {
                // 如果有相同类型，则合并它们的total和scored
                JSONObject scoreTypeObj1 = (JSONObject) scoreTypeMap1.get(type);
                BigDecimal total1 = scoreTypeObj1.getBigDecimal("total", BigDecimal.ZERO);
                BigDecimal scored1Item = scoreTypeObj1.getBigDecimal("scored", BigDecimal.ZERO);
                BigDecimal total2 = scoreTypeObj2.getBigDecimal("total", BigDecimal.ZERO);
                BigDecimal scored2Item = scoreTypeObj2.getBigDecimal("scored", BigDecimal.ZERO);

                scoreTypeObj1.set("total", total1.add(total2));
                scoreTypeObj1.set("scored", scored1Item.add(scored2Item));
            }
        }

        // 返回合并后的结果
        return JSONUtil.createObj()
                .set("totalScore", totalScore)
                .set("scored", scored)
                .set("additionalScore", additionalScore)
                .set("additionalScored", additionalScored)
                .set("scoreTypeMap", scoreTypeMap1);
    }


    public JSONObject getScore() {
        BigDecimal totalScore = BigDecimal.ZERO;
        BigDecimal additionalScore = BigDecimal.ZERO;
        BigDecimal additionalScored = BigDecimal.ZERO;
        BigDecimal scored = BigDecimal.ZERO;
        if (Objects.isNull(config)) {
            return null;
        }
        JSONObject configObj = config.getConfigObj();
        if (!configObj.getBool("score", false)) {
            return JSONUtil.createObj().set("totalScore", totalScore).set("scored", scored);
        }
        Map<String, JSONObject> scoreTypeMap = new HashMap<>();
        JSONArray scoreTypes = configObj.getJSONArray("scoreTypes");
        if (Objects.nonNull(scoreTypes)) {
            scoreTypes.forEach(type -> {
                scoreTypeMap.put(type.toString(), new JSONObject());
            });
        }

        JSONArray reviewed = getReviewedObj();
        JSONArray areas = config.getAreasObj();
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            if (!areas.getJSONObject(areaIdx).getBool("enabled", true)) {
                continue;
            }
            JSONArray questions = areas.getJSONObject(areaIdx).getJSONArray("questions");

            for (int qsIdx = 0; qsIdx < questions.size(); qsIdx++) {
                JSONObject qs = questions.getJSONObject(qsIdx);
//                BigDecimal score = qs.getBigDecimal("score");

                // 修改后：
                String rawScore = qs.getStr("score");
                if (rawScore != null) {
                    rawScore = rawScore.replaceAll("[^\\d.-]", "");
                }
                BigDecimal score = NumberUtil.toBigDecimal(StrUtil.isBlank(rawScore) ? "0" : rawScore);

                int additional = qs.getInt("isAdditional", 1);
                String scoreType = qs.getStr("scoreType");
                if (scoreType.equals("4")) {
                    scoreType = config.getAreasObj().getJSONObject(areaIdx).getJSONArray("questions").getJSONObject(qsIdx).getStr("scoreType");
                }
                String isCorrect = reviewed.getByPath(String.format("[%s].reviewed[%s].isCorrect", areaIdx, qsIdx),
                        String.class);

                //BigDecimal scorePointScore = NumberUtil.toBigDecimal(reviewed.getByPath(String.format("[%s].reviewed[%s].scored", areaIdx, qsIdx), String.class));

                // 修改后（修复逻辑）：
                String rawScored = reviewed.getByPath(String.format("[%s].reviewed[%s].scored", areaIdx, qsIdx), String.class);
                // 清理非数字字符（保留数字、小数点、负号）
                if (rawScored != null) {
                    rawScored = rawScored.replaceAll("[^\\d.-]", "");
                }
                // 处理空字符串情况
                if (StrUtil.isBlank(rawScored)) {
                    rawScored = "0"; // 默认值
                }
                BigDecimal scorePointScore = NumberUtil.toBigDecimal(rawScored);

                scorePointScore = BigDecimal.ZERO;
                try {
                    Object value = reviewed.getByPath(String.format("[%s].reviewed[%s].scored", areaIdx, qsIdx), String.class);
                    if (value != null) {
                        scorePointScore = NumberUtil.toBigDecimal(value.toString());
                    }
                } catch (Exception e) {
                    // 处理异常，这里可以根据需要添加日志记录等操作
                }
                String isScorePoint = String.valueOf(Optional.ofNullable(reviewed.getByPath(String.format("[%s].reviewed[%s].isScorePoint", areaIdx, qsIdx))).orElse("1"));
                if (Objects.nonNull(scorePointScore) && "1".equals(isScorePoint)) {
                    scorePointScore = BigDecimal.ZERO;
                }
                if (StrUtil.isNotBlank(isCorrect)) {
                    if (isCorrect.equals("Y") && !isScorePoint.equals("2")) {
                        scorePointScore = score;
                    } else if (isCorrect.equals("N")) {
                        scorePointScore = BigDecimal.ZERO;
                    }
                }
                if (additional == 1) {
                    totalScore = totalScore.add(score);
                    scored = scored.add(scorePointScore);

                    if (Objects.nonNull(scoreType) && scoreTypeMap.containsKey(scoreType)) {
                        JSONObject scoreTypeObj = scoreTypeMap.get(scoreType);
                        scoreTypeObj.set("total", scoreTypeObj.getBigDecimal("total", BigDecimal.ZERO)
                                .add(score));
                        scoreTypeObj.set("scored", scoreTypeObj.getBigDecimal("scored", BigDecimal.ZERO)
                                .add(scorePointScore));
                    }
                } else {
                    additionalScore = additionalScore.add(score);
                    additionalScored = additionalScored.add(scorePointScore);
                }
            }
        }
        return JSONUtil.createObj()
                .set("totalScore", totalScore)
                .set("scored", scored)
                .set("additionalScore", additionalScore)
                .set("additionalScored", additionalScored)
                .set("scoreTypeMap", scoreTypeMap);
    }


    /**
     * 废弃
     *
     * @return
     */
    public JSONObject getScore2() {
        BigDecimal totalScore = BigDecimal.ZERO;
        BigDecimal additionalScore = BigDecimal.ZERO;
        BigDecimal additionalScored = BigDecimal.ZERO;
        BigDecimal scored = BigDecimal.ZERO;
        if (Objects.isNull(config)) {
            return null;
        }
        JSONObject configObj = config.getConfigObj();
        if (!configObj.getBool("score", false)) {
            return JSONUtil.createObj().set("totalScore", totalScore).set("scored", scored);
        }
        Map<String, JSONObject> scoreTypeMap = new HashMap<>();

        JSONArray reviewed = getReviewedObj();
        JSONArray areas = config.getAreasObj();
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            if (!areas.getJSONObject(areaIdx).getBool("enabled", true)) {
                continue;
            }
            JSONArray questions = areas.getJSONObject(areaIdx).getJSONArray("questions");

            for (int qsIdx = 0; qsIdx < questions.size(); qsIdx++) {
                JSONObject qs = questions.getJSONObject(qsIdx);
                BigDecimal qsTotal = qs.getBigDecimal("score");
                String scoreType = qs.getStr("scoreType");
                String qsScoredStr = reviewed.getByPath(String.format("[%s].reviewed[%s].scored", areaIdx, qsIdx),
                        String.class);
                BigDecimal qsScored = BigDecimal.ZERO;
                if (StrUtil.isNotBlank(qsScoredStr)) {
                    qsScored = NumberUtil.toBigDecimal(qsScoredStr);
                } else {
                    String qsIsCorrectStr = reviewed.getByPath(String.format("[%s].reviewed[%s].isCorrect", areaIdx, qsIdx),
                            String.class);
                    if (StrUtil.isBlank(qsIsCorrectStr)) {
                        qsScored = qsTotal;
                    } else {
                        boolean qsIsCorrect = Boolean.parseBoolean(qsIsCorrectStr);
                        if (qsIsCorrect) {
                            qsScored = qsTotal;
                        }
                    }
                }

                if (!scoreTypeMap.containsKey(scoreType)) {
                    scoreTypeMap.put(scoreType, new JSONObject());
                }
                if (Objects.nonNull(scoreType) && scoreTypeMap.containsKey(scoreType)) {
                    JSONObject scoreTypeObj = scoreTypeMap.get(scoreType);
                    scoreTypeObj.set("total", scoreTypeObj.getBigDecimal("total", BigDecimal.ZERO)
                            .add(qsTotal));
                    scoreTypeObj.set("scored", scoreTypeObj.getBigDecimal("scored", BigDecimal.ZERO)
                            .add(qsScored));
                }
                totalScore = totalScore.add(qsTotal);
                scored = scored.add(qsScored);
            }
        }

        return JSONUtil.createObj()
                .set("totalScore", totalScore)
                .set("scored", scored)
                .set("scoreTypeMap", scoreTypeMap);
    }

    /**
     * 根据给定格式合成分数字符串，自动累加各类型下的 total 与 scored，
     * 并在末尾追加"总分"条目。
     *
     * @param totalScore   总分总额
     * @param score        已得总分
     * @param scoreTypes   各类型分数映射（类型名 → JSONObject/Map/POJO 等）
     * @param scoreFormat  分数格式编号，用于从配置中获取格式化模板
     * @param scoreTypesArr 类型名顺序列表，用于控制输出顺序
     * @return 格式化后的分数文本
     */
    public static String scoreTxt(BigDecimal totalScore,
                                  BigDecimal score,
                                  Map<String, Object> scoreTypes,
                                  int scoreFormat,
                                  List<String> scoreTypesArr) {
        // 1. 获取格式化配置
        CorrectConfigConsts.ScoreFormat fmt =
                CorrectConfigConsts.SCORE_FORMAT_MAP.get(scoreFormat);

        // 2. 统一映射，避免 null 分支
        Map<String, Object> types = Optional
                .ofNullable(scoreTypes)
                .orElse(Collections.emptyMap());

        // 3. 确定需累加的类型键列表（排除"总分"且映射中存在）
        List<String> keys = scoreTypesArr == null
                ? new ArrayList<>(types.keySet())
                : scoreTypesArr.stream()
                .filter(k -> !"总分".equals(k) && types.containsKey(k))
                .collect(Collectors.toList());

        // 4. 使用 StringJoiner 累积各类型条目
        StringJoiner joiner = new StringJoiner(fmt.getDelimiter());
        for (String k : keys) {
            joiner.add(formatEntry(k, types.get(k), fmt));
        }

        // 5. 构造最终字符串
        StringBuilder sb = new StringBuilder();
        String body = joiner.toString();
        if (!body.isEmpty()) {
            sb.append(body)
                    .append(fmt.getLastDelimiter())
                    .append(" ");
        }
        // 末尾追加"总分"
        sb.append(MessageFormat.format(
                fmt.getPattern(),
                "总分",
                score,
                totalScore,
                totalScore.subtract(score)
        ));

        // 6. 去掉可能的多余前缀（如 " = 总分:"）
        String result = sb.toString()
                .replaceFirst("^\\s*" + Pattern.quote(fmt.getLastDelimiter()), "");
        return result;
    }

    /**
     * 将单个"类型 → 分数对象"格式化为一段文本。
     * @param key   类型名称
     * @param obj   分数对象（可为 JSONObject/Map/POJO 等）
     * @param fmt   格式化模板
     * @return 格式化后的字符串段
     */
    private static String formatEntry(String key,
                                      Object obj,
                                      CorrectConfigConsts.ScoreFormat fmt) {
        JSONObject json = (obj instanceof JSONObject)
                ? (JSONObject) obj
                : JSONUtil.parseObj(obj);

        BigDecimal total  = json.getBigDecimal("total",  BigDecimal.ZERO);
        BigDecimal scored = json.getBigDecimal("scored", BigDecimal.ZERO);
        BigDecimal remain = total.subtract(scored);

        return MessageFormat.format(
                fmt.getPattern(),
                key, scored, total, remain
        );
    }
    /**
     * 通用方法，用于从不同对象中获取属性值（这里假设 HashAdapter 和 JSONObject 都支持 get(String) 方法）
     */
    private Object getValue(Object obj, String key) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof HashAdapter) {
            return ((HashAdapter) obj).get(key);
        } else if (obj instanceof JSONObject) {
            return ((JSONObject) obj).get(key);
        }
        return null;
    }

    /**
     * 获取BigDecimal值，并转换（假设Convert.toBigDecimal能处理合适的转换）
     */
    private BigDecimal getBigDecimalValue(Object obj, String key) {
        Object value = getValue(obj, key);
        return Convert.toBigDecimal(value);
    }

    public String getLevelRange(JSONObject allScore, JSONObject totalScoreArea, JSONArray scoreTypes) {
        StringBuilder result = new StringBuilder();
        result.append("$");
        LevelRangeObj res = new LevelRangeObj();
        if (Objects.isNull(enableLevelOutput) || !enableLevelOutput) {
            return "";
        }
        res.setTotalScoreArea(totalScoreArea);
        JSONObject scoreTypeMap = allScore.getJSONObject("scoreTypeMap");

        List<LevelRangeObj.LevelScores> levelRanges = new ArrayList<>();
        // 每个类型
        for (Object scoreTypesItem : scoreTypes) {
            String type = (String) scoreTypesItem;
            if (scoreTypeMap.containsKey(type) && !type.equals("总分")) {
                BigDecimal scored = scoreTypeMap.getJSONObject(type).getBigDecimal("scored");
                BigDecimal total = scoreTypeMap.getJSONObject(type).getBigDecimal("total");
                LevelRangeObj.LevelScores item = getLevelRangesItem(scored, total, type);
                if (Objects.nonNull(item)) {
                    levelRanges.add(item);
                    result.append(String.format("%s^{%s}  \\quad ", item.getLevel(), item.getScore()));
                }
            }
        }
        // 总分
        BigDecimal scored = allScore.getBigDecimal("scored");
        BigDecimal total = allScore.getBigDecimal("totalScore");
        LevelRangeObj.LevelScores item = getLevelRangesItem(scored, total, "总分");
        if (Objects.nonNull(item)) {
            levelRanges.add(item);
            result.append(String.format("%s^{%s}  \\quad ", item.getLevel(), item.getScore()));
        }

        res.setLevelRangesList(levelRanges);
        result.append("$");
        return result.toString();
    }

    public LevelRangeObj.LevelScores getLevelRangesItem(BigDecimal scored, BigDecimal total, String type) {

        BigDecimal percentage = BigDecimal.valueOf(100L)
                .subtract(scored.divide(total, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        JSONArray rangesTypeArr = ranges.getJSONArray(type);
        // 判断是那个类型
        for (Object rangeItem : rangesTypeArr) {
            JSONObject range = (JSONObject) rangeItem;
            BigDecimal maxx = range.getBigDecimal("maxx");
            BigDecimal minx = range.getBigDecimal("minn");
            String name = range.getStr("name");
            if (scored.subtract(maxx).compareTo(BigDecimal.ZERO) <= 0 && scored.subtract(minx).compareTo(BigDecimal.ZERO) >= 0) {
                LevelRangeObj.LevelScores levelScores = new LevelRangeObj.LevelScores();
                levelScores.setLevel(name);
                levelScores.setScore(String.valueOf(scored.subtract(total)));
                return levelScores;
            }
        }
        return null;
    }



    /**
     * 将两个 Map<String, Object> 按键合并，并对同键的 "total" 与 "scored" 字段累加。
     * 对 null 值、非 JSONObject 值都做了保护，保证不会 NPE。
     *
     * @param score1 第一组分数映射，value 可为 JSONObject、Map、POJO、JSON 字符串等
     * @param score2 第二组分数映射，同上
     * @return 合并后包含所有键的 JSONObject
     */
    public static JSONObject mergeScoreType(Map<String, Object> score1,
                                            Map<String, Object> score2) {
        JSONObject merged = JSONUtil.createObj();

        // 1) 插入 score1 的所有键值
        if (score1 != null) {
            for (Map.Entry<String, Object> e : score1.entrySet()) {
                merged.put(e.getKey(), toJSONObject(e.getValue()));
            }
        }

        // 2) 遍历 score2，合并到 merged
        if (score2 != null) {
            for (Map.Entry<String, Object> e : score2.entrySet()) {
                String key = e.getKey();
                JSONObject o2  = toJSONObject(e.getValue());
                Object    prev = merged.get(key);

                if (!(prev instanceof JSONObject)) {
                    // merged 中没有 JSONObject 存在，或存在但不是 JSONObject -> 直接覆盖／插入
                    merged.put(key, o2);
                } else {
                    // prev 肯定是 JSONObject，不会再 NPE
                    JSONObject o1 = (JSONObject) prev;

                    BigDecimal t1 = safeGetBigDecimal(o1, "total");
                    BigDecimal s1 = safeGetBigDecimal(o1, "scored");
                    BigDecimal t2 = safeGetBigDecimal(o2, "total");
                    BigDecimal s2 = safeGetBigDecimal(o2, "scored");

                    o1.put("total",  t1.add(t2));
                    o1.put("scored", s1.add(s2));
                }
            }
        }

        return merged;
    }

    /**
     * 任意对象转 JSONObject。
     * 如果已经是 JSONObject，直接返回；否则交给 JSONUtil.parseObj。
     */
    private static JSONObject toJSONObject(Object val) {
        if (val instanceof JSONObject) {
            return (JSONObject) val;
        }
        return JSONUtil.parseObj(val);
    }

    /**
     * 安全地从 JSONObject 中取 BigDecimal：
     * - 如果 key 不存在或值为 null，返回 0
     * - 如果值转换失败，也返回 0
     */
    private static BigDecimal safeGetBigDecimal(JSONObject o, String key) {
        try {
            return o.getBigDecimal(key, BigDecimal.ZERO);
        } catch (Exception ex) {
            return BigDecimal.ZERO;
        }
    }


    public Boolean getJsonschema() {
        if (Objects.isNull(this.task)) {
            if (StrUtil.isNotBlank(this.aimodel)) {
                return this.aimodel.contains("doubao") ? defaultJsonSchemaValueForDouBao : defaultJsonSchemaValueForOtherModal;
            } else {
                return CorrectConfigConsts.defaultJsonSchemaValue;
            }
        } else {
            return this.getTask().getJsonschema();
        }
    }

    public Boolean getJsonobject() {
        if (Objects.isNull(this.task)) {
            if (StrUtil.isNotBlank(this.aimodel)) {
                return this.aimodel.contains("doubao") ? defaultJsonObjectValueForDouBao : defaultJsonObjectValueForOtherModal;
            } else {
                return CorrectConfigConsts.defaultJsonObjectValue;
            }
        } else {
            return this.getTask().getJsonobject();
        }
    }
}

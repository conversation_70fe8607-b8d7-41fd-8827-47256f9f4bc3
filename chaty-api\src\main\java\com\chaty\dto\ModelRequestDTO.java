package com.chaty.dto;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.entity.ModelRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ModelRequestDTO extends ModelRequest {
    // 分页参数
    private PageDTO<?> page;

    private JSONObject promptObj;

    public JSONObject getPromptObj() {
        if (StrUtil.isNotBlank(getPrompt())) {
            promptObj = JSONUtil.parseObj(getPrompt());
            return promptObj;
        } else {
            return new JSONObject();
        }
    }
} 
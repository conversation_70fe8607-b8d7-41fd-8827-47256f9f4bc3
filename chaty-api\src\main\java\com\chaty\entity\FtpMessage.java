package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ftp_message")
public class FtpMessage {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String schoolName;
    
    private LocalDateTime notificationTime;

    private Integer notificationCount;

    private String filePath;
    
    private String fileName;
    
    private String fileDetail;

    private Boolean fileCompleted;

    private Boolean configPackageCompleted;

    private Boolean exportCompleted;

    private String remark;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;

    /**
     * 导出完成时间
     */
    private LocalDateTime exportCompletedTime;

    private Double width;

    private Double height;

    private String topic;
    /**
     * 第一页的
     */
    private String imgUrl;

    /**
     * 第一页的
     */
    private String docUrl;

    private Boolean isCollect;

    private String docType;

    /**
     * 文件备注
     */
    private String fileRemark;

    private Integer pdfPaperSize;

    /**
     * 相同标准卷的ftpmessage对象
     */
    private String sameToOthersPaperId;

    private Integer configPackagePaperNumber;

    private Boolean isInAgent;
}
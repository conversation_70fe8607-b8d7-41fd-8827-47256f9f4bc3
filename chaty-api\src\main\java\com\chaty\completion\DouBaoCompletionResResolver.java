package com.chaty.completion;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.api.essayScoring.EssayScoreApi;
import com.chaty.dto.CorrectQsDTO;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.enums.AIModelConsts;
import com.chaty.exception.BaseException;
import com.tencentcloudapi.ecc.v20181213.models.CorrectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Service
public class DouBaoCompletionResResolver implements CompletionResResolver {
    @Resource
    private EssayScoreApi essayScoreApi;

    /**
     * 重写了这个：resolveDocAreaRes
     *
     * @param record
     * @param completionRes
     * @return
     */
    @Override
    public JSONObject resolveDocAreaRes(DocCorrectRecordDTO record, Map<String, Object> completionRes) {
        return resolveDocAreaResponseFormatRes(record, completionRes);
    }

    @Override
    public boolean isSupported(String aimodel, Boolean jsonSchema) {
        if (Boolean.TRUE.equals(jsonSchema)) {
            // 这是function_call的，不支持json_schema
            return false;
        }
        return AIModelConsts.douBaoResolveModels.contains(aimodel);
    }

    @Override
    public List<CorrectQsDTO> resolveRxtraQsRes(Map<String, Object> completionRes) {
        String rawResp = getResultStr(completionRes);
        String regexTemplate = "(?<=##\\s?%s\\s?\\n)[\\d\\D]*?(?=((\\n(##|---)|$)))";

        Map<String, String> regKeyMap = MapUtil.builder("学生手写回答", "studentAnswer")
                .put("题目", "question")
                .put("答案", "answer")
                .put("答案的相关信息", "questionInfo")
                .build();
        List<CorrectQsDTO> qss = new ArrayList<>();
        regKeyMap.forEach((k, v) -> {
            String regex = String.format(regexTemplate, k);
            List<String> matched = ReUtil.findAll(regex, rawResp, 0);
            for (int index = 0; index < matched.size(); index++) {
                if (qss.size() == index) {
                    qss.add(new CorrectQsDTO());
                }
                CorrectQsDTO qs = qss.get(index);
                BeanUtil.setProperty(qs, v, matched.get(index).trim());
            }
        });
        return qss;
    }

    @Override
    public List<CorrectQsDTO> resolveExtraQsResWithRespFormat(Map<String, Object> completionRes) {
        JSONArray resp = getResultObj(completionRes).getJSONArray("questions");
        return resp.toList(CorrectQsDTO.class);
    }

    private static JSONObject getResultObj(Map<String, Object> completionRes) {
        String rawResp = getResultStr(completionRes);
        return JSONUtil.parseObj(rawResp);
    }


    private static String getResultStr(Map<String, Object> completionRes) {
        JSONObject resObj = JSONUtil.parseObj(completionRes);
        Object args = resObj.getByPath("choices[0].message.tool_calls[0].function.arguments");
        return (String) args;
    }

    /**
     * 已完成
     *
     * @param record
     * @param completionRes
     * @return
     */
    @Override
    public JSONObject resolveDocAreaResponseFormatRes(DocCorrectRecordDTO record, Map<String, Object> completionRes) {
        String rawStr = getResultStr(completionRes);
        JSONObject finallyResult = JSONUtil.parseObj(rawStr);
        JSONObject step3FinResult = finallyResult.getJSONObject("第五步具体批改");
        JSONArray reviewed = new JSONArray();
        JSONObject res = new JSONObject();

        // 健壮获取题目数量，获取失败则不限制数量
        int questionCount = -1; // -1表示不限制
        try {
            JSONArray areas = record.getConfig() != null ? record.getConfig().getAreasObj() : null;
            if (areas != null && !areas.isEmpty()) {
                JSONObject area = areas.getJSONObject(0);
                JSONArray questions = area.getJSONArray("questions");
                if (questions != null) {
                    questionCount = questions.size();
                }
            }
        } catch (Exception e) {
            log.error("获取题目数量失败，将全部解析", e);
        }

        int parsedCount = 0;
        for (String qsId : step3FinResult.keySet()) {
            // 只处理“题目X”key
            if (!qsId.matches("题目\\d+")) continue;
            if (questionCount > 0 && parsedCount >= questionCount) break; // 只在获取成功时限制
            try {
                JSONObject qsResObj = (JSONObject) step3FinResult.get(qsId);
                CompletionResResolver.getNormalQsResponse(reviewed, qsResObj);
                parsedCount++;
            } catch (Exception e) {
                log.error("解析题目{}失败，跳过", qsId, e);
            }
        }

        res.set("reviewed", reviewed);
        return res;
    }

    /**
     * 已完成
     *
     * @param record
     * @param completionRes
     * @return
     */
    @Override
    public JSONObject resolveDocAreaResponseFirstRequestRes(DocCorrectRecordDTO record, Map<String, Object> completionRes) {
        String rawStr = getResultStr(completionRes);
        JSONObject finallyResult = JSONUtil.parseObj(rawStr);
        JSONObject step3FinResult = finallyResult.getJSONObject("第五步具体批改");
        JSONArray reviewed = new JSONArray();
        JSONObject res = new JSONObject();

        // 健壮获取题目数量，获取失败则不限制数量
        int questionCount = -1; // -1表示不限制
        try {
            JSONArray areas = record.getConfig() != null ? record.getConfig().getAreasObj() : null;
            if (areas != null && !areas.isEmpty()) {
                JSONObject area = areas.getJSONObject(0);
                JSONArray questions = area.getJSONArray("questions");
                if (questions != null) {
                    questionCount = questions.size();
                }
            }
        } catch (Exception e) {
            log.error("获取题目数量失败，将全部解析", e);
        }

        int parsedCount = 0;
        for (String qsId : step3FinResult.keySet()) {
            // 只处理“题目X”key
            if (!qsId.matches("题目\\d+")) continue;
            if (questionCount > 0 && parsedCount >= questionCount) break; // 只在获取成功时限制
            try {
                String studentAnswer = step3FinResult.getStr(qsId);
                JSONObject qsResObj = new JSONObject();
                qsResObj.set("学生答案", studentAnswer);
                CompletionResResolver.getNormalQsResponse(reviewed, qsResObj);
                parsedCount++;
            } catch (Exception e) {
                log.error("解析题目{}失败，跳过", qsId, e);
            }
        }

        res.set("reviewed", reviewed);
        return res;
    }




    /**
     * 已完成json升级
     *
     * @param record
     * @param completionRes
     * @param allScore
     * @return
     */
    @Override
    public JSONObject resolveScoreRes(DocCorrectRecordDTO record, Map<String, Object> completionRes, Float allScore) {
        Float score = 0F;
        Float completionResScore = 0F;
        String rawResp = getResultStr(completionRes);

        log.info("score completionRes: {}", completionRes);
        try {
            JSONObject response = JSONUtil.parseObj(rawResp);
            // 获取分数字段
            Float scoreFloat = response.getFloat("最终分数");
            if (Objects.nonNull(scoreFloat)) {
                try {
                    completionResScore = scoreFloat;
                    score = allScore + completionResScore;
                } catch (NumberFormatException e) {
                    log.error("Invalid score format: {}", scoreFloat, e);
                    score = allScore;
                }
            } else {
                log.error("Score field is missing.");
            }
        } catch (Exception e) {
            log.error("解析分数失败", e);
            // 识别失败或没有将分数设置为最高分
            score = Float.valueOf(allScore);
        }

        if (score < 0) {
            score = 0F;
        } else if (score > allScore) {
            score = Float.valueOf(allScore);
        }
        JSONArray reviewed = new JSONArray();
        JSONObject qsReviewed = new JSONObject();
        qsReviewed.set("studentAnswer", score);
        qsReviewed.set("isCorrect", "Y");
        qsReviewed.set("review", "识别老师分数，分数为" + completionResScore);
        qsReviewed.set("scored", score);
        qsReviewed.set("isScorePoint", 2);
        qsReviewed.set("isTeacherScore", true);
        reviewed.add(qsReviewed);
        JSONObject res = new JSONObject();
        res.set("reviewed", reviewed);
        JSONArray reviewList = new JSONArray();
        reviewList.add(reviewed);
        res.set("reviewList", reviewList);
        return res;
    }


    /**
     * 作文
     *
     * @param record
     * @param completionRes
     * @param allScore                  总分
     * @param tencentCorrectData        腾讯云的学生原文批改结果
     * @param tencentCorrectDataRewrite 腾讯云的提升作文批改结果
     * @return
     */
    @Override
    public JSONObject resolveDocWriteQsRes(DocCorrectRecordDTO record,
                                           Map<String, Object> completionRes,
                                           Integer allScore,
                                           CorrectData tencentCorrectData,
                                           CorrectData tencentCorrectDataRewrite) {
        String rawResp = getResultStr(completionRes);
        JSONObject content = JSONUtil.parseObj(rawResp);

        Integer maxScore = allScore - 3;
        JSONArray reviewed = new JSONArray();
        JSONObject res = new JSONObject();
        JSONObject qsReviewed = new JSONObject();
        qsReviewed.set("studentAnswer", content.getStr("学生作文全文"));
        qsReviewed.set("isCorrect", "Y");
        qsReviewed.set("review", content);
        qsReviewed.set("scored", content.get("评分"));
        qsReviewed.set("isEssay", true);
        qsReviewed.set("answer", content.getStr("作文重写"));
        qsReviewed.set("studentName", content.getStr("学生姓名"));
        qsReviewed.set("tencentCorrectData", tencentCorrectData);
        qsReviewed.set("tencentCorrectDataRewrite", tencentCorrectDataRewrite);
        // 四个小分的分数
        qsReviewed.set("subScores", getSubScores(tencentCorrectData));
        qsReviewed.set("subScoresRewrite", getSubScores(tencentCorrectDataRewrite));
        qsReviewed.set("isEnglishEssay", true);
        // 分数使用essayScore获取
        try {
            Float score = Math.min(maxScore, calculateTotalScore(tencentCorrectData));
            qsReviewed.set("scored", score);
        } catch (Exception e) {
            // 默认70分
            qsReviewed.set("scored", Math.min(allScore, 70 + ThreadLocalRandom.current().nextInt(0, 15)));
            log.error("获取分数失败", e);
        }

        try {
            // 重写后的分数要比之前的高
            Integer score = qsReviewed.getInt("scored");
            Float scoreRewrite = calculateTotalScore(tencentCorrectDataRewrite);
            if (scoreRewrite + 10 < score) {
                qsReviewed = adjustSubScores(tencentCorrectDataRewrite, score - 10, qsReviewed);
                scoreRewrite = (float) Math.min(maxScore, qsReviewed.getInt("scoreRewrite"));
                content.set("重写后的分数", scoreRewrite);
            } else {
                scoreRewrite = Math.min(maxScore, scoreRewrite);
                content.set("重写后的分数", scoreRewrite);
            }
        } catch (Exception e) {
            Integer score = qsReviewed.getInt("scored");
            content.set("重写后的分数", Math.min(allScore - 3, score + ThreadLocalRandom.current().nextInt(10, 20)));
            log.error("获取重写分数失败", e);
        }

        completionRes.put("$response", JSONUtil.toJsonStr(content));
        reviewed.add(qsReviewed);
        res.set("reviewed", reviewed);
        return res;
    }

    /**
     * 作文-华师
     *
     * @param record
     * @param allScore
     * @param huaShiApiResult
     * @return
     */
    @Override
    public JSONObject resolveDocWriteQsHuaShiRes(DocCorrectRecordDTO record,
                                                 Integer allScore,
                                                 JSONObject huaShiApiResult) {
        if (huaShiApiResult == null) {
            log.error("huashi api response is null");
        }
        Integer maxScore = allScore;
        JSONArray reviewed = new JSONArray();
        JSONObject res = new JSONObject();
        JSONObject qsReviewed = new JSONObject();
        String studentAnswer = huaShiApiResult.getStr("title");
        JSONArray text = huaShiApiResult.getJSONArray("text");
        for (int i = 0; i < text.size(); i++) {
            JSONArray texts = text.getJSONArray(i);
            String content = "";
            for (int j = 0; j < texts.size(); j++) {
                content += texts.getStr(j);
            }
            studentAnswer = studentAnswer + "\n" + content;
        }
        qsReviewed.set("studentAnswer", studentAnswer);
        qsReviewed.set("isCorrect", "Y");
        qsReviewed.set("review", huaShiApiResult);
        qsReviewed.set("isEssay", true);
        qsReviewed.set("answer", "无作文重写");
        qsReviewed.set("studentName", "无学生姓名");
        // 语文作文批改，直接采用原作文分数，提升后的作文分数比原作文分数 大于等于
        qsReviewed.set("huaShiApiResult", huaShiApiResult);
        qsReviewed.set("subScores", getSubScoresFromHuaShi(huaShiApiResult));
        qsReviewed.set("isEnglishEssay", false);
        qsReviewed.set("aiEvaluation", huaShiApiResult.getJSONObject("aiEvaluation"));
        try {
            Integer score = Math.min(maxScore, calculateTotalScoreFromHuaShi(huaShiApiResult));
            qsReviewed.set("scored", score);
        } catch (Exception e) {
            // 默认70分
            qsReviewed.set("scored", 0);
            log.error("获取分数失败", e);
        }
        reviewed.add(qsReviewed);
        res.set("reviewed", reviewed);
        return res;
    }

    private String getSubScoresFromHuaShi(JSONObject response) {
        if (response == null) {
            log.error("huashia api response is null");
            return "数据异常";
        }

        JSONObject res = response.getJSONObject("aiEvaluation");
        if (res == null) {
            log.error("aiEvaluation is missing in response: {}", response);
            return "数据异常";
        }

        Integer expressionScore = 0;
        try {
            JSONObject expressionEval = res.getJSONObject("expressionEvaluation");
            if (expressionEval != null) {
                expressionScore = expressionEval.getInt("expressionScore");
            }
        } catch (Exception e) {
            log.error("huashia api cannot get expressionScore response:{}", res);
        }

        Integer fluencyScore = 0;
        try {
            JSONObject fluencyEval = res.getJSONObject("fluencyEvaluation");
            if (fluencyEval != null) {
                fluencyScore = fluencyEval.getInt("fluencyScore");
            }
        } catch (Exception e) {
            log.error("huashia api cannot get fluencyScore response:{}", res);
        }

        Integer topicRelevanceScore = 0;
        try {
            JSONObject overallEval = res.getJSONObject("overallEvaluation");
            if (overallEval != null) {
                topicRelevanceScore = overallEval.getInt("topicRelevanceScore");
            }
        } catch (Exception e) {
            log.error("huashia api cannot get topicRelevanceScore response:{}", res);
        }

        Integer wordSentenceScore = 0;
        try {
            JSONObject wordSentenceEval = res.getJSONObject("wordSentenceEvaluation");
            if (wordSentenceEval != null) {
                wordSentenceScore = wordSentenceEval.getInt("wordSentenceScore");
            }
        } catch (Exception e) {
            log.error("huashia api cannot get wordSentenceScore response:{}", res);
        }

        String format = "表达:%s 流畅:%s 主题:%s 词句:%s";
        return String.format(format, expressionScore, fluencyScore, topicRelevanceScore, wordSentenceScore);
    }


    private Integer calculateTotalScoreFromHuaShi(JSONObject response) {
        JSONObject res = response.getJSONObject("aiEvaluation");
        Integer expressionScore = 0;
        try {
            expressionScore = res.getJSONObject("expressionEvaluation").getInt("expressionScore");
        } catch (Exception e) {
            log.error("huashia api cannot get expressionScore response:{}", res);
        }

        Integer fluencyScore = 0;
        try {
            fluencyScore = res.getJSONObject("fluencyEvaluation").getInt("fluencyScore");
        } catch (Exception e) {
            log.error("huashia api cannot get fluencyScore response:{}", res);
        }

        Integer topicRelevanceScore = 0;
        try {
            topicRelevanceScore = res.getJSONObject("overallEvaluation").getInt("topicRelevanceScore");
        } catch (Exception e) {
            log.error("huashia api cannot get topicRelevanceScore response:{}", res);
        }

        Integer wordSentenceScore = 0;
        try {
            wordSentenceScore = res.getJSONObject("wordSentenceEvaluation").getInt("wordSentenceScore");
        } catch (Exception e) {
            log.error("huashia api cannot get wordSentenceScore response:{}", res);
        }
        return expressionScore + fluencyScore + topicRelevanceScore + wordSentenceScore;
    }

    private JSONObject adjustSubScores(CorrectData tencentCorrectDataRewrite, Integer requiredScore, JSONObject qsReviewed) {
        // 获取当前四个小分
        float currentWordsScore = scoreChange(tencentCorrectDataRewrite.getScoreCat().getWords().getScore()) * tencentCorrectDataRewrite.getScoreCat().getWords().getPercentage() / 100;
        float currentSentencesScore = scoreChange(tencentCorrectDataRewrite.getScoreCat().getSentences().getScore()) * tencentCorrectDataRewrite.getScoreCat().getSentences().getScore() / 100;
        float currentStructureScore = scoreChange(tencentCorrectDataRewrite.getScoreCat().getStructure().getScore()) * tencentCorrectDataRewrite.getScoreCat().getStructure().getScore() / 100;
        float currentContentScore = scoreChange(tencentCorrectDataRewrite.getScoreCat().getContent().getScore()) * tencentCorrectDataRewrite.getScoreCat().getContent().getScore() / 100;

        // 计算需要调整的总分
        float totalCurrentScore = currentWordsScore + currentSentencesScore + currentStructureScore + currentContentScore;
        float difference = requiredScore - totalCurrentScore;

        // 计算每个小分增加的值
        float adjustmentPerScore = difference / 4;

        // 调整每个小分
        currentWordsScore = (currentWordsScore + adjustmentPerScore) / tencentCorrectDataRewrite.getScoreCat().getWords().getPercentage();
        currentSentencesScore = (currentSentencesScore + adjustmentPerScore) / tencentCorrectDataRewrite.getScoreCat().getSentences().getPercentage();
        currentStructureScore = (currentStructureScore + adjustmentPerScore) / tencentCorrectDataRewrite.getScoreCat().getStructure().getPercentage();
        currentContentScore = (currentContentScore + adjustmentPerScore) / tencentCorrectDataRewrite.getScoreCat().getContent().getPercentage();

        qsReviewed.set("subScoresRewrite", getSubScores2(currentWordsScore, currentSentencesScore, currentStructureScore, currentContentScore,
                tencentCorrectDataRewrite.getScoreCat().getWords().getPercentage(),
                tencentCorrectDataRewrite.getScoreCat().getSentences().getPercentage(),
                tencentCorrectDataRewrite.getScoreCat().getStructure().getPercentage(),
                tencentCorrectDataRewrite.getScoreCat().getContent().getPercentage()));
        Integer scoreRewrite = Math.round(currentContentScore + currentWordsScore + currentSentencesScore + currentStructureScore);
        qsReviewed.set("scoreRewrite", scoreRewrite);
        return qsReviewed;
    }

    private static Float scoreChange(Float score) {
        // 计算根号，乘以 10，并限制范围在 0 到 100
        float result = (float) (Math.sqrt(score) * 10);
        return Math.max(0, Math.min(result, 100));  // 限制范围在0到100之间
    }

    private static String getSubScores(CorrectData tencentCorrectData) {
        String subScoresFormat = "词汇:%s %s%% 句子:%s %s%% 篇章结构: %s %s%% 内容:%s %s%%";

        // 计算各个分数并通过 scoreChange 方法处理
        String subScores = String.format(subScoresFormat,
                String.format("%.2f", scoreChange(tencentCorrectData.getScoreCat().getWords().getScore())),
                tencentCorrectData.getScoreCat().getWords().getPercentage(),
                String.format("%.2f", scoreChange(tencentCorrectData.getScoreCat().getSentences().getScore())),
                tencentCorrectData.getScoreCat().getSentences().getPercentage(),
                String.format("%.2f", scoreChange(tencentCorrectData.getScoreCat().getStructure().getScore())),
                tencentCorrectData.getScoreCat().getStructure().getPercentage(),
                String.format("%.2f", scoreChange(tencentCorrectData.getScoreCat().getContent().getScore())),
                tencentCorrectData.getScoreCat().getContent().getPercentage()
        );
        return subScores;
    }

    private static String getSubScores2(Float currentWordsScore,
                                        Float currentSentencesScore,
                                        Float currentStructureScore,
                                        Float currentContentScore,
                                        Float percent1,
                                        Float percent2,
                                        Float percent3,
                                        Float percent4) {
        String subScoresFormat = "词汇:%s %s%% 句子:%s %s%% 篇章结构: %s %s%% 内容:%s %s%%";

        // 计算各个分数并通过 scoreChange 方法处理
        String subScores = String.format(subScoresFormat,
                String.format("%.2f", currentWordsScore),
                percent1,
                String.format("%.2f", currentSentencesScore),
                percent2,
                String.format("%.2f", currentStructureScore),
                percent3,
                String.format("%.2f", currentContentScore),
                percent4
        );
        return subScores;
    }

    private static Float calculateTotalScore(CorrectData tencentCorrectData) {
        // 计算各个分数并通过 scoreChange 方法处理
        float wordsScore = scoreChange(tencentCorrectData.getScoreCat().getWords().getScore()) * tencentCorrectData.getScoreCat().getWords().getPercentage() / 100;
        float sentencesScore = scoreChange(tencentCorrectData.getScoreCat().getSentences().getScore()) * tencentCorrectData.getScoreCat().getSentences().getPercentage() / 100;
        float structureScore = scoreChange(tencentCorrectData.getScoreCat().getStructure().getScore()) * tencentCorrectData.getScoreCat().getStructure().getPercentage() / 100;
        float contentScore = scoreChange(tencentCorrectData.getScoreCat().getContent().getScore()) * tencentCorrectData.getScoreCat().getContent().getPercentage() / 100;

        float totalScore = wordsScore + sentencesScore + structureScore + contentScore;
        totalScore = Math.round(totalScore * 100.0f) / 100.0f;
        return totalScore;
    }


    public static int getRandomInt() {
        return ThreadLocalRandom.current().nextInt(-10, 21); // 上限是排他的，所以设为21
    }

    @Override
    public String resolveEssayAnalyticalReportDocRes(Map<String, Object> completionRes) {
        return (String) completionRes.get("$response");
    }

    @Override
    public String resolveStudentNameRes(Map<String, Object> completionRes) {
        return (String) completionRes.get("$response");
    }

    @Override
    public String resolveStudentNumberRes(Map<String, Object> completionRes) {
        return (String) completionRes.get("$response");
    }

    @Override
    public PaperTopicDTO resolvePaperTopicRes(Map<String, Object> completionRes) {
        try {
            String response = (String) completionRes.get("$response");
            return JSONUtil.toBean(response, PaperTopicDTO.class);
        } catch (Exception e) {
            log.error("提取topic失败 {}", completionRes, e);
            return null;
        }
    }


}

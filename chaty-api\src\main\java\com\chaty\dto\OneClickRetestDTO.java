package com.chaty.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OneClickRetestDTO {

    @NotBlank(message = "fileId 不能为空")
    private String fileId;

    @NotNull(message = "modelRequestId 不能为空")
    private Integer modelRequestId;

    @NotBlank(message = "newName 不能为空")
    private String newName;

    private Boolean needNewConfig = false;
}

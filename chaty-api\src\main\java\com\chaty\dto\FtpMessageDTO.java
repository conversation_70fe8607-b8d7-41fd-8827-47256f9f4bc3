package com.chaty.dto;

import com.chaty.entity.DocCorrectFile;
import com.chaty.entity.DocCorrectConfigPackage;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FtpMessageDTO {
    
    private Integer id;
    
    @JsonProperty("schoolName")
    private String schoolName;
    
    @JsonProperty("notificationTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime notificationTime;
    
    @JsonProperty("filePath")
    private String filePath;

    @JsonProperty("notificationCount")
    private Integer notificationCount;
    
    @JsonProperty("fileName")
    private String fileName;
    
    private String fileDetail;

    @JsonProperty("fileCompleted")
    private Boolean fileCompleted;

    @JsonProperty("configPackageCompleted")
    private Boolean configPackageCompleted;

    @JsonProperty("exportCompleted")
    private Boolean exportCompleted;

    @JsonProperty("exportCompletedTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exportCompletedTime;

    private String remark;

    private String fileId;

    private DocCorrectFile docCorrectFile;
    private DocCorrectConfigPackage docCorrectConfigPackage;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // 分页参数
    private PageDTO<?> page;

    /**
     * 是否只查询今天的数据
     */
    private Boolean isSelectToday;

    private Double width;

    private Double height;

    private String topic;

    private String imgUrl;

    private String docUrl;

    private Boolean isCollect;

    private String docType;

    @JsonProperty("fileRemark")
    private String fileRemark;

    private Integer pdfPaperSize;

    private Integer isCorrectFinish;

    /**
     * 相同标准卷的ftpmessage对象
     */
    @JsonProperty("sameToOthersPaperId")
    private String sameToOthersPaperId;

    private Integer configPackagePaperNumber;
}
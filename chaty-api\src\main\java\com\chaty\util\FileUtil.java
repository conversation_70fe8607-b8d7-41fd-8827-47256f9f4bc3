package com.chaty.util;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FileUtil {

    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;

    public static FileUtil INSTANCE = null;

    @PostConstruct
    public void postConstrect() {
        FileUtil.INSTANCE = this;
    }

    /**
     * eg: http://ip:port/static/1.jpg -> /tmp/1.jpg
     */
    public String url2Path(String url) {
        return url.replace(serverUrl + ctxPath, path);
    }

    /**
     * eg: /static/1.jpg -> /tmp/1.jpg
     */
    public String ctxUrl2Path(String url) {
        return url.replace(ctxPath, path);
    }

    public String relUrl2Path(String url) {
        return url.replace(ctxPath, path);
    }

    /**
     * eg: /tmp/1.jpg -> 1.jpg
     */
    public String path2Filename(String filePath) {
        return filePath.replace(path, "");
    }
    
    /**
     * eg: /static/1.jpg -> 1.jpg
     */
    public String ctxPath2Filename(String filePath) {
        return filePath.replace(ctxPath, "");
    }

    public String url2Base64(String url) {
        BufferedImage image = null;
        if (url.startsWith(serverUrl)) {
            // 本地文件
            String filePath = url2Path(url);
            image = ImgUtil.read(filePath);
        } else if (url.startsWith(ctxPath)) {
            // 以 ctxPath 开头的相对 URL，例如 /static/1.jpg
            String filePath = ctxUrl2Path(url);
            image = ImgUtil.read(filePath);
        } else {
            image = ImgUtil.read(URLUtil.url(url));
        }
        return ImgUtil.toBase64(image, "jpg");
    }

    public String docAreaImg(String docPath, int x, int y, int width, int height) {
        return docAreaImg(docPath, x, y, width, height, null, 0, false);
    }

    public String docAreaImg(String docPath, int x, int y, int width, int height, int rightRotation) {
        return docAreaImg(docPath, x, y, width, height, null, rightRotation, false);
    }

    /**
     * 默认上下左右放大 5 个字符
     * @param docPath
     * @param x
     * @param y
     * @param width
     * @param height
     * @param rightRotation
     * @return
     */
    public String docAreaImg(String docPath, int x, int y, int width, int height, int rightRotation, Boolean enlarge) {
        return docAreaImg(docPath, x, y, width, height, null, rightRotation, enlarge);
    }

    public String docAreaImg(String docPath, int x, int y, int width, int height, List<JSONObject> markAreas, Integer rightRotation, Boolean enlarge) {
        String absDocPath = String.format("%s/%s", path, docPath);
        String imgName = IdUtil.fastSimpleUUID() + ".jpg";
        String imgPath = String.format("%s/%s", path, imgName);
        if (enlarge) {
            x -= 80;
            y -= 80;
            width += 160;
            height += 160;
        }
        PDFUtil.extractImageFromPDF(absDocPath, imgPath, x, y, width, height, markAreas, rightRotation);

        ThreadUtil.sleep(1000); // 等待图片生成

        return imgName;
    }

    /**
     * 多区域图片直接生成
     */
    public JSONArray setDocAreasImg(String docPath, JSONArray areas, Integer offsetX, Integer offsetY) {
        if (Objects.isNull(offsetX)) {
            offsetX = 0;
        }
        if (Objects.isNull(offsetY)) {
            offsetY = 0;
        }
        String absDocPath = String.format("%s/%s", path, docPath);
        BufferedImage image = PDFUtil.getDocumentImage(absDocPath);
        JSONArray res = new JSONArray();
        for (int i = 0; i < areas.size(); i++) {
            JSONObject areaObj = areas.getJSONObject(i);
            JSONObject area = areaObj.getJSONObject("area");
            if (Objects.nonNull(area)) {
                String imgName = IdUtil.fastSimpleUUID() + ".jpg";
                String imagePath = String.format("%s/%s", path, imgName);
                int x = area.getInt("x") + offsetX;
                int y = area.getInt("y") + offsetY;
                int width = area.getInt("width");
                int height = area.getInt("height");
                // 自动扩宽
//                x -= 80;
//                y -= 80;
//                width += 160;
//                height += 160;

                PDFUtil.extractImageFromImage(image, imagePath, x, y, width, height, getQsAreas(areaObj));
                // 设置图片路径
                areaObj.set("areaImg", String.format("%s/%s", ctxPath, imgName));
            }
            res.add(areaObj);
        }
        ThreadUtil.sleep(1000); // 睡一下，防止程序读到未保存的图片
        return res;
    }

    /**
     * 多区域图片直接生成
     */
    public JSONArray setDocAreasImg(String docPath, JSONArray areas) {
        return setDocAreasImg(docPath, areas, 0, 0);
    }

    /**
     * 获取题目对应的区域
     */
    public List<JSONObject> getQsAreas(JSONObject areaObj) {
        JSONObject area = areaObj.getJSONObject("area");
        return areaObj.getJSONArray("questions").stream()
                .map(qs -> {
                    JSONObject qsPosArea = ((JSONObject) qs).getJSONObject("qsPosArea");
                    if (Objects.nonNull(qsPosArea)) {
                        qsPosArea.set("x", qsPosArea.getInt("x") - area.getInt("x"));
                        qsPosArea.set("y", qsPosArea.getInt("y") - area.getInt("y"));
                    }
                    return qsPosArea;
                })
                .filter(a -> Objects.nonNull(a))
                .collect(Collectors.toList());
    }

    public String absPath(String relPath) {
        return String.format("%s/%s", path, relPath);
    }

    public String docUrl2ImgUrl(String docPath) {
        String filename = docPath.substring(docPath.lastIndexOf("/") + 1).split(("\\."))[0];
        String imgName = String.format("%s.%s", filename, "jpg");
        PDFUtil.convert2Img(relUrl2Path(docPath), String.format("%s/%s", path, imgName));
        return String.format("%s/%s", ctxPath, imgName);
    }

    String[] ZIP_FILE_SUFFIX = {"不含原卷", "含原卷", "统计结果"};

    public String zipUrls(String name, List<String> docUrls) {
        // 压缩文件
        String zipName = String.format("%s.zip", name);
        String zipPath = String.format("%s/%s", path, zipName);
        String srcPath = String.format("%s/%s", path, IdUtil.fastSimpleUUID());
        for (int i = 0; i < docUrls.size(); i++) {
            String url = docUrls.get(i);
            String path = relUrl2Path(url);
            cn.hutool.core.io.FileUtil.copy(path, String.format("%s/%s(%s).pdf", srcPath, name, ZIP_FILE_SUFFIX[i]),
                    true);
        }
        ZipUtil.zip(srcPath, zipPath, false);
        return String.format("%s/%s", ctxPath, zipName);
    }

    /**
     * 预加载文件
     * 
     * @param url eg: /static/1.pdf
     */
    public void preLoadFile(String url) {
        log.info("预加载文件: url-{}", url);
        // 获取文件名
        String path = ctxUrl2Path(url);
        // 判断文件是否存在
        log.info("预加载文件: path-{}", path);
        File file = cn.hutool.core.io.FileUtil.file(path);
        if (file.exists()) {
            // 如果文件存在，直接返回
            return;
        }
        // 请求服务器下载文件
        String downloadUrl = String.format("%s%s", serverUrl, url);
        log.info("预加载文件: downloadUrl-{}", downloadUrl);
        // 下载文件
        HttpUtil.downloadFile(downloadUrl, file);
    }

    /**
     * 将文件路径转换为 InputStream
     *
     * @param filePath 文件路径
     * @return InputStream
     * @throws IOException 如果文件读取失败
     */
    public static InputStream convertFileToInputStream(String filePath) throws IOException {
        // 创建文件对象
        File file = new File(filePath);

        // 如果文件不存在则抛出异常
        if (!file.exists()) {
            throw new IOException("文件不存在：" + filePath);
        }

        // 创建并返回文件的 InputStream
        return new FileInputStream(file);
    }


    /**
     * 将给定的 ctxPath 形式的图片 URL 旋转 180° 并保存为新文件，
     * 返回新的 ctxPath 访问地址。
     *
     * @param ctxImageUrl 以 ctxPath 开头的图片 URL（如 "/static/xxx.jpg"）
     * @return 新生成的旋转后图片的 ctxPath URL（如 "/static/yyy.jpg"）
     * @throws IOException 如果读取或写入图片文件失败
     */
    public String rotateImage180(String ctxImageUrl) throws IOException {
        String absFilePath = relUrl2Path(ctxImageUrl);

        BufferedImage srcImage = ImgUtil.read(absFilePath);
        if (srcImage == null) {
            throw new IOException("无法读取图片文件：" + absFilePath);
        }

        Image rotatedImg = ImgUtil.rotate(srcImage, 180);
        BufferedImage rotatedBuf = toBufferedImage(rotatedImg);

        String extension = ctxImageUrl.substring(ctxImageUrl.lastIndexOf('.') + 1);
        String newFileName = IdUtil.fastSimpleUUID() + "." + extension;
        String newFilePath = String.format("%s/%s", path, newFileName);
        File outFile = new File(newFilePath);

        ImgUtil.write(rotatedBuf, outFile);

        return String.format("%s/%s", ctxPath, newFileName);
    }

    /**
     * 工具：将任意 java.awt.Image 转为 BufferedImage
     */
    private BufferedImage toBufferedImage(Image img) {
        if (img instanceof BufferedImage) {
            return (BufferedImage) img;
        }
        BufferedImage bimage = new BufferedImage(
                img.getWidth(null),
                img.getHeight(null),
                BufferedImage.TYPE_INT_RGB
        );
        Graphics2D bGr = bimage.createGraphics();
        bGr.drawImage(img, 0, 0, null);
        bGr.dispose();
        return bimage;
    }


    /**
     * 将给定 ctxPath 形式的单页 PDF 旋转 180° 并保存为新文件，
     * 返回新的 ctxPath 访问地址。
     *
     * @param ctxPdfUrl 以 ctxPath 开头的 PDF URL（如 "/static/xxx.pdf"）
     * @return 新生成的旋转后 PDF 的 ctxPath URL（如 "/static/yyy.pdf"）
     * @throws IOException 如果读取或写入 PDF 文件失败
     */
    public String rotatePdf180(String ctxPdfUrl) throws IOException {
        String absFilePath = relUrl2Path(ctxPdfUrl);
        File srcFile = new File(absFilePath);
        if (!srcFile.exists()) {
            throw new IOException("文件不存在：" + absFilePath);
        }

        try (PDDocument document = PDDocument.load(srcFile)) {
            if (document.getNumberOfPages() > 0) {
                PDPage page = document.getPage(0);
                int newRotation = (page.getRotation() + 180) % 360;
                page.setRotation(newRotation);
            }

            String newFileName = IdUtil.fastSimpleUUID() + ".pdf";
            String newFilePath = String.format("%s/%s", path, newFileName);
            document.save(newFilePath);

            return String.format("%s/%s", ctxPath, newFileName);
        }
    }

    /**
     * 保存 Base64 图片到本地，并返回可通过 ctxPath 访问的 URL。
     *
     * @param base64Str Base64 字符串，可以含 data URI 前缀
     * @return 图片的 ctxPath 访问地址，例如 "/static/xxx.jpg"
     * @throws IOException 如果写入文件失败
     */
    public String saveBase64Image(String base64Str) throws IOException {
        // 去掉可能的 data URI 前缀
        String pureBase64 = base64Str.contains(",")
                ? base64Str.substring(base64Str.indexOf(',') + 1)
                : base64Str;

        // 用 JDK Base64 解码
        byte[] imageBytes = Base64.getDecoder().decode(pureBase64);

        // 根据前缀推断扩展名，否则默认 jpg
        String extension = "jpg";
        if (base64Str.startsWith("data:")) {
            String mime = base64Str.substring(5, base64Str.indexOf(';'));
            extension = mime.substring(mime.indexOf('/') + 1);
        }

        // 随机文件名
        String fileName = IdUtil.fastSimpleUUID() + "." + extension;
        // 本地完整路径
        Path outPath = Paths.get(path, fileName);

        // 写入文件
        Files.write(outPath, imageBytes);

        // 返回通过 ctxPath 访问的 URL
        return ctxPath + "/" + fileName;
    }
}

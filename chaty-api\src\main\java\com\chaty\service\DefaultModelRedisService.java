package com.chaty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.entity.ModelRequest;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CorrectConfigConsts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 默认模型配置存储服务。
 * 在 Spring 启动时初始化默认模型各项属性到 Redis（若已存在则不覆盖），
 * 并提供获取、批量更新、单个修改、重置与查询全部配置的接口。
 */
@Slf4j
@Service
public class DefaultModelRedisService implements InitializingBean {
    private static final String KEY_PREFIX = "defaultModel:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /** 保存各配置属性原始值，用于重置和默认值回退 */
    private final Map<String, String> originalDefaults = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        // 增加id字段默认值
        putIfAbsent("id", "-1");
        putIfAbsent("name", AIModelConsts.GPT_DEFAULT_LABEL);
        putIfAbsent("modelValue", AIModelConsts.GPT_DEFAULT);
        putIfAbsent("jsonobject", CorrectConfigConsts.defaultJsonObjectValue);
        putIfAbsent("jsonschema", CorrectConfigConsts.defaultJsonSchemaValueForDouBao);
        putIfAbsent("content", "");
        putIfAbsent("remark", "");
        putIfAbsent("weight", "0");
    }

    private void putIfAbsent(String suffix, Object value) {
        String key = KEY_PREFIX + suffix;
        String strVal = Objects.toString(value, "");
        Boolean exists = redisTemplate.hasKey(key);
        if (Boolean.FALSE.equals(exists)) {
            redisTemplate.opsForValue().set(key, strVal);
        }
        originalDefaults.put(suffix, strVal);
    }

    // -----------------------
    // Getter：若 Redis 中无值，则返回初始化默认值
    // -----------------------

    public Integer getId() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "id");
        String fallback = originalDefaults.get("id");
        String toParse = val != null ? val : fallback;
        try {
            return Integer.valueOf(toParse);
        } catch (NumberFormatException e) {
            log.warn("Invalid id stored in Redis or default: {}", toParse);
            return 0;
        }
    }

    public String getName() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "name");
        return val != null ? val : originalDefaults.get("name");
    }

    public String getModelValue() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "modelValue");
        return val != null ? val : originalDefaults.get("modelValue");
    }

    public Boolean getJsonobject() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "jsonobject");
        String fallback = originalDefaults.get("jsonobject");
        return Boolean.parseBoolean(val != null ? val : fallback);
    }

    public Boolean getJsonschema() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "jsonschema");
        String fallback = originalDefaults.get("jsonschema");
        return Boolean.parseBoolean(val != null ? val : fallback);
    }

    public String getContent() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "content");
        return val != null ? val : originalDefaults.get("content");
    }
    public JSONObject getContentObj() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "content");
        if (StrUtil.isBlank(val)) {
            return null;
        } else {
            return JSONUtil.parseObj(val);
        }
    }

    public String getRemark() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "remark");
        return val != null ? val : originalDefaults.get("remark");
    }

    public Integer getWeight() {
        String val = redisTemplate.opsForValue().get(KEY_PREFIX + "weight");
        String fallback = originalDefaults.get("weight");
        String toParse = val != null ? val : fallback;
        try {
            return Integer.valueOf(toParse);
        } catch (NumberFormatException e) {
            log.warn("Invalid weight stored in Redis or default: {}", toParse);
            return 0;
        }
    }

    // -----------------------
    // 批量更新
    // -----------------------

    public void update(ModelRequest req) {
        if (req == null) return;
        if (req.getId() != null) updateValue(KEY_PREFIX + "id", req.getId().toString());
        if (req.getName() != null) updateValue(KEY_PREFIX + "name", req.getName());
        if (req.getModelValue() != null) updateValue(KEY_PREFIX + "modelValue", req.getModelValue());
        if (req.getJsonobject() != null) updateValue(KEY_PREFIX + "jsonobject", req.getJsonobject().toString());
        if (req.getJsonschema() != null) updateValue(KEY_PREFIX + "jsonschema", req.getJsonschema().toString());
        if (req.getContent() != null) updateValue(KEY_PREFIX + "content", req.getContent());
        if (req.getRemark() != null) updateValue(KEY_PREFIX + "remark", req.getRemark());
        if (req.getWeight() != null) updateValue(KEY_PREFIX + "weight", req.getWeight().toString());
    }

    // -----------------------
    // 单字段更新/重置
    // -----------------------

    public void updateValue(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void resetValue(String key) {
        String suffix = key.startsWith(KEY_PREFIX) ? key.substring(KEY_PREFIX.length()) : key;
        String original = originalDefaults.get(suffix);
        if (original != null) {
            redisTemplate.opsForValue().set(key, original);
        }
    }

    // -----------------------
    // 查询全部
    // -----------------------

    public Map<String, String> getAllDefaultConfig() {
        Set<String> keys = redisTemplate.keys(KEY_PREFIX + "*");
        Map<String, String> result = new HashMap<>();
        if (keys != null) {
            for (String k : keys) {
                String suf = k.substring(KEY_PREFIX.length());
                result.put(suf, redisTemplate.opsForValue().get(k));
            }
        }
        return result;
    }

    // -----------------------
    // 整体获取
    // -----------------------

    public ModelRequest getModelRequest() {
        ModelRequest req = new ModelRequest();
        req.setId(getId());
        req.setName(getName());
        req.setModelValue(getModelValue());
        req.setJsonobject(getJsonobject());
        req.setJsonschema(getJsonschema());
        req.setContent(getContent());
        req.setRemark(getRemark());
        req.setWeight(getWeight());
        req.setCreateTime(LocalDateTime.now());
        req.setUpdateTime(LocalDateTime.now());
        return req;
    }
}

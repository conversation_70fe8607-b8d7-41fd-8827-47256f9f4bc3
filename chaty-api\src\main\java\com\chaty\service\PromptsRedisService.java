package com.chaty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.dto.ModelRequestDTO;
import com.chaty.entity.PromptChangeLog;
import com.chaty.enums.PromptsConsts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 该服务在 Spring 启动时会将 PromptsConsts 中的所有常量存入 Redis（key 格式为统一前缀 + 常量名，
 * 如果 redis 中已经存在该 key 的值则不覆盖），同时提供各个常量的 getter 方法，以及修改、重置方法和获取所有 key 与中文描述的方法。
 */
@Slf4j
@Service
public class PromptsRedisService implements InitializingBean {

    // 统一前缀，例如 "prompts:"，可以根据需要调整
    private static final String KEY_PREFIX = "prompts:";
    private static final String KEY_PREFIX_NAME = "promptsName:";
    private static final String KEY_PREFIX_TYPE = "promptsType:";
    private static final String MODEL_REQUEST_KEY_PREFIX = "modelRequest:";

    private static final String KEY_LIST = "config:keyList";

    private final List<String> keysList = new CopyOnWriteArrayList<>(Arrays.asList(
    ));


    /*
        * 存储各常量描述的映射，key 为常量名（不含前缀），value 为中文描述
     */
    private final Map<String, String> keyMap = new ConcurrentHashMap<>();
    private final Map<String, String> typeMap = new ConcurrentHashMap<>();

    /** 添加大题类型：普通题目和两轮请求第一轮 */
    public void addQuestionType(String name, String value) {
        String suffix = String.valueOf(UUID.randomUUID());
        String normalSuffix = "common_Question" + suffix;
        String firstReqSuffix = "common_Question_first_request" + suffix;

        String normalKey = KEY_PREFIX + normalSuffix;
        String firstReqKey = KEY_PREFIX + firstReqSuffix;

        String normalKeyName = KEY_PREFIX_NAME + normalSuffix;
        String firstReqKeyName = KEY_PREFIX_NAME + firstReqSuffix;

        String normalKeyType = KEY_PREFIX_TYPE + normalSuffix;
        String firstReqKeyType = KEY_PREFIX_TYPE + firstReqSuffix;
        // 写入 Redis
        redisTemplate.opsForValue().set(normalKeyName, name);
        redisTemplate.opsForValue().set(firstReqKeyName, name);

        redisTemplate.opsForValue().set(normalKeyType, "普通题目-大题类型" );
        redisTemplate.opsForValue().set(firstReqKeyType, "两轮请求-第一轮-大题类型");

        // 记录原始值，供 reset 使用
        originalConstants.put(normalSuffix, name);
        originalConstants.put(firstReqSuffix, name);

        // 把 suffix 同步到 keysList，保证 listAutoByKeyNameAndType 能检索到
        keysList.add(normalSuffix);
        keysList.add(firstReqSuffix);
        saveKeyList();
        // 写类型和描述
        keyMap.put(normalKey, name);
        keyMap.put(firstReqKey,  name);
        typeMap.put(normalKey, "普通题目-大题类型");
        typeMap.put(firstReqKey,  "两轮请求-第一轮-大题类型");

        updateValue(normalKey, value);
        updateValue(firstReqKey, value);
    }

    /**
     * deleteKey 方法：删除指定的 key
     */
    public void deleteKey(String key) {
        String suffix = key.startsWith(KEY_PREFIX)
                ? key.substring(KEY_PREFIX.length())
                : key;

        // 2. 从内存列表中移除
        keysList.remove(suffix);

        saveKeyList();
    }

    /**
     * 把当前内存中的 keysList 序列化存入 Redis
     */
    public void saveKeyList() {
        // 序列化成 JSON
        String json = JSONUtil.toJsonStr(keysList);
        // 存入 Redis
        redisTemplate.opsForValue().set(KEY_LIST, json);
    }

    /**
     * 从 Redis 取出 keyList 字段，反序列化为 List<String>
     * 如果 Redis 中没有，返回一个空列表
     */
    public List<String> getKeyList() {
        String json = redisTemplate.opsForValue().get(KEY_LIST);
        if (StrUtil.isBlank(json)) {
            return Collections.emptyList();
        }
        try {
            return JSONUtil.toList(json, String.class);
        } catch (Exception e) {
            log.error("解析 Redis 中的 keyList 失败，json=" + json, e);
            return Collections.emptyList();
        }
    }

    /**
     * 刷新内存中的 keysList：从 Redis 重新读取然后替换
     */
    public void refreshKeyList() {
        List<String> list = getKeyList();
        if (list.isEmpty()) {
            log.warn("refreshKeyList: Redis 中没有 keyList，可调用 saveKeyList() 先保存一次");
            return;
        }
        // 清空并替换
        keysList.clear();
        keysList.addAll(list);
        log.info("refreshKeyList: 已从 Redis 加载 {} 个 suffix", list.size());
    }

    /**
     * 用新的列表替换内存中的 keysList，并持久化到 Redis
     *
     * @param keysListInput 新的 suffix 列表
     */
    public void setKeysList(List<String> keysListInput) {
        if (keysListInput == null) {
            log.warn("setKeysList: 输入列表为 null，忽略操作");
            return;
        }
        // 清空旧的并添加所有新元素
        keysList.clear();
        keysList.addAll(keysListInput);

    }

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private PromptChangeLogService promptChangeLogService;

    /**
     * 存储各常量原始值的映射，key 为常量名（不含前缀）
     */
    private final Map<String, String> originalConstants = new HashMap<>();

    private void initStaticKeyMap() {
        keyMap.put(KEY_PREFIX + "DOC_CORRECT", "批改提示-角色描述-已废弃");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE", "批改结果内容模板-已废弃");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_V1", "批改提示V1-已废弃");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE_V1", "批改结果内容模板V1-已废弃");
        keyMap.put(KEY_PREFIX + "EXTRA_QS_TEMPLATE", "题目识别模板-已废弃");
        keyMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_TEMPLETE", "题目识别结构化输出模板-必填");
        keyMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA", "题目识别响应格式（豆包）");
        keyMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_SCHEMA", "题目识别响应格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_V2", "批改提示V2-已废弃");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT", "普通题目批改提示词-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST", "普通题目-两次批改-第一次-批改提示词-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_SECOND_REQUEST", "普通题目-两次批改-第二次-批改提示词-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE", "批改响应格式模板-已废弃");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1", "批改响应格式Schema1");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2", "批改响应格式Schema2");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3", "批改响应格式Schema3");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4", "批改响应格式Schema4");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5", "批改响应格式Schema5");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT", "写作批改响应格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT", "写作批改响应格式（豆包）");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2", "写作批改响应格式2");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2", "写作批改响应格式2（豆包）");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT", "写作批改系统提示");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2", "写作批改系统提示2");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT", "学生姓名提纯提示-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT", "学生姓名提纯格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT", "学生学号提纯提示-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT", "学生学号提纯格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_SYSTEM_PROMPT", "老师分数识别提示-必填");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_FORMAT", "老师分数识别响应格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_PROMPT", "作文报告提示");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT", "作文报告豆包提示");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT", "作文报告响应格式");
        keyMap.put(KEY_PREFIX + "Doubao_Common_Question_Return_Structure", "豆包-普通题目-返回json格式");
//        keyMap.put(KEY_PREFIX + "Normal_Qs_First_Common_Question_Return_Structure", "普通题目-两次询问-第一次-返回json格式");
        keyMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT", "豆包-识别分数-返回json格式");

        keyMap.put(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_PROMPT", "标准卷相似度判断-必填");
        keyMap.put(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE", "标准卷相似度判断 json structure");

        keyMap.put(KEY_PREFIX + "common_Question2", "单选题");
        keyMap.put(KEY_PREFIX + "common_Question3", "多选题");
        keyMap.put(KEY_PREFIX + "common_Question4", "判断题");
        keyMap.put(KEY_PREFIX + "common_Question5", "填空题");
        keyMap.put(KEY_PREFIX + "common_Question6", "简单的四则运算");
        keyMap.put(KEY_PREFIX + "common_Question7", "数学计算题");
        keyMap.put(KEY_PREFIX + "common_Question8", "数学应用题");
        keyMap.put(KEY_PREFIX + "common_Question9", "连线题");
        keyMap.put(KEY_PREFIX + "common_Question10", "画图题");
        keyMap.put(KEY_PREFIX + "common_Question11", "翻译题");
        keyMap.put(KEY_PREFIX + "common_Question12", "图表题");
        keyMap.put(KEY_PREFIX + "common_Question13", "涂卡题");


        keyMap.put(KEY_PREFIX + "common_Question_first_request2", "单选题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request3", "多选题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request4", "判断题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request5", "填空题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request6", "简单的四则运算");
        keyMap.put(KEY_PREFIX + "common_Question_first_request7", "数学计算题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request8", "数学应用题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request9", "连线题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request10", "画图题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request11", "翻译题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request12", "图表题");
        keyMap.put(KEY_PREFIX + "common_Question_first_request13", "涂卡题");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT", "废弃");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE", "废弃");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_V1", "废弃");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE_V1", "废弃");
        typeMap.put(KEY_PREFIX + "EXTRA_QS_TEMPLATE", "废弃");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_V2", "废弃");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE", "废弃");

        typeMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_TEMPLETE", "题目识别");
        typeMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA", "题目识别");
        typeMap.put(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_SCHEMA", "题目识别");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_SECOND_REQUEST", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4", "普通题目");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5", "普通题目");
        typeMap.put(KEY_PREFIX + "Doubao_Common_Question_Return_Structure", "普通题目");
//        typeMap.put(KEY_PREFIX + "Normal_Qs_First_Common_Question_Return_Structure", "普通题目");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT", "写作");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT", "写作");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2", "写作");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2", "写作");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT", "写作");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2", "写作");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT", "姓名/学号");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT", "姓名/学号");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT", "姓名/学号");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT", "姓名/学号");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_SYSTEM_PROMPT", "分数识别");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_FORMAT", "分数识别");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT", "分数识别");

        typeMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_PROMPT", "作文报告");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT", "作文报告");
        typeMap.put(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT", "作文报告");

        typeMap.put(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_PROMPT", "标注卷匹配");
        typeMap.put(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE", "标注卷匹配");

        typeMap.put(KEY_PREFIX + "common_Question2", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question3", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question4", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question5", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question6", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question7", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question8", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question9", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question10", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question11", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question12", "普通题目-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question13", "普通题目-大题类型");

        typeMap.put(KEY_PREFIX + "common_Question_first_request2", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request3", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request4", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request5", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request6", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request7", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request8", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request9", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request10", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request11", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request12", "两轮请求-第一轮-大题类型");
        typeMap.put(KEY_PREFIX + "common_Question_first_request13", "两轮请求-第一轮-大题类型");
    }

    private void initRedisKeyMap () {
        // 初始化 Redis 中的常量描述映射
        for (String key : keysList) {
            String redisKey = KEY_PREFIX + key;
            String redisNameKey = KEY_PREFIX_NAME + key;
            String redisTypeKey = KEY_PREFIX_TYPE + key;

            if (redisTemplate.hasKey(redisNameKey)) {
                keyMap.put(redisKey, redisTemplate.opsForValue().get(redisNameKey));
            }
            if (redisTemplate.hasKey(redisTypeKey)) {
                typeMap.put(redisKey, redisTemplate.opsForValue().get(redisTypeKey));
            }
        }
    }

    /**
     * 启动时初始化 Redis 中的常量（如果 key 不存在则存入），同时保存原始值到 originalConstants 中
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        refreshKeyList();
        initStaticKeyMap();
        initRedisKeyMap();
        putIfAbsent("DOC_CORRECT", PromptsConsts.DOC_CORRECT);
        putIfAbsent("DOC_CORRECT_CONTENT_TEMPLATE", PromptsConsts.DOC_CORRECT_CONTENT_TEMPLATE);
        putIfAbsent("DOC_CORRECT_V1", PromptsConsts.DOC_CORRECT_V1);
        putIfAbsent("DOC_CORRECT_CONTENT_TEMPLATE_V1", PromptsConsts.DOC_CORRECT_CONTENT_TEMPLATE_V1);
        putIfAbsent("EXTRA_QS_TEMPLATE", PromptsConsts.EXTRA_QS_TEMPLATE);
        putIfAbsent("EXTRA_QS_RESPFORMAT_TEMPLETE", PromptsConsts.EXTRA_QS_RESPFORMAT_TEMPLETE);
        putIfAbsent("EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA", PromptsConsts.EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA);
        putIfAbsent("EXTRA_QS_RESPFORMAT_SCHEMA", PromptsConsts.EXTRA_QS_RESPFORMAT_SCHEMA);
        putIfAbsent("DOC_CORRECT_V2", PromptsConsts.DOC_CORRECT_V2);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_SECOND_REQUEST", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_SECOND_REQUEST);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4);
        putIfAbsent("DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5", PromptsConsts.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5);
        putIfAbsent("DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT", PromptsConsts.DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT);
        putIfAbsent("DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT", PromptsConsts.DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT);
        putIfAbsent("DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2", PromptsConsts.DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2);
        putIfAbsent("DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2", PromptsConsts.DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2);
        putIfAbsent("DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT", PromptsConsts.DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT);
        putIfAbsent("DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2", PromptsConsts.DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2);
        putIfAbsent("DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT", PromptsConsts.DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT);
        putIfAbsent("DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT", PromptsConsts.DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT);
        putIfAbsent("DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT", PromptsConsts.DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT);
        putIfAbsent("DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT", PromptsConsts.DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT);
        putIfAbsent("DOC_CORRECT_SCORE_SYSTEM_PROMPT", PromptsConsts.DOC_CORRECT_SCORE_SYSTEM_PROMPT);
        putIfAbsent("DOC_CORRECT_SCORE_RESPONSE_FORMAT", PromptsConsts.DOC_CORRECT_SCORE_RESPONSE_FORMAT);
        putIfAbsent("DOC_CORRECT_ESSAY_REPORT_PROMPT", PromptsConsts.DOC_CORRECT_ESSAY_REPORT_PROMPT);
        putIfAbsent("DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT", PromptsConsts.DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT);
        putIfAbsent("DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT", PromptsConsts.DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT);
        putIfAbsent("Doubao_Common_Question_Return_Structure", PromptsConsts.Doubao_Common_Question_Return_Structure);
//        putIfAbsent("Normal_Qs_First_Common_Question_Return_Structure", PromptsConsts.Normal_Qs_First_Common_Question_Return_Structure);
        putIfAbsent("DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT", PromptsConsts.DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT);

        putIfAbsent("DOC_EXTRACT_PAPER_TROPIC_PROMPT", PromptsConsts.DOC_EXTRACT_PAPER_TROPIC_PROMPT);
        putIfAbsent("DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE", PromptsConsts.DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE);

        putIfAbsent("common_Question2", "单选题提示模板");
        putIfAbsent("common_Question3", "多选题提示模板");
        putIfAbsent("common_Question4", "判断题提示模板");
        putIfAbsent("common_Question5", "填空题提示模板");
        putIfAbsent("common_Question6", "简单的四则运算提示模板");
        putIfAbsent("common_Question7", "数学计算题提示模板");
        putIfAbsent("common_Question8", "数学应用题提示模板");
        putIfAbsent("common_Question9", "连线题提示模板");
        putIfAbsent("common_Question10", "画图题提示模板");
        putIfAbsent("common_Question11", "翻译题提示模板");
        putIfAbsent("common_Question12", "图表题提示模板");
        putIfAbsent("common_Question13", "涂卡题提示模板");
    }

    /**
     * 内部方法：如果 redis 中不存在 key，则存入，同时保存到 originalConstants 中
     */
    private void putIfAbsent(String keySuffix, String value) {
        String redisKey = KEY_PREFIX + keySuffix;
        Boolean exists = redisTemplate.hasKey(redisKey);
        if (exists == null || !exists) {
            redisTemplate.opsForValue().set(redisKey, value);
        }
        // 保存原始内容，后续用于重置
        originalConstants.put(keySuffix, value);
    }

    public void setModelRequest(ModelRequestDTO modelRequestDTO) {
        if (Objects.isNull(modelRequestDTO)) {
            return;
        }
        if (Objects.nonNull(modelRequestDTO.getId())) {
            // 删除里面的所有键
            deleteModelRequest(modelRequestDTO.getId());

            JSONObject promptObj = modelRequestDTO.getPromptObj();
            for(String key : promptObj.keySet()) {
                redisTemplate.opsForValue().set(getModelRequestPromptKey(modelRequestDTO.getId(), key), (String) promptObj.getOrDefault(key, ""));
            }
        }
    }

    public void deleteModelRequest(Integer modelRequestId) {
        String keyPrefix = KEY_PREFIX + MODEL_REQUEST_KEY_PREFIX + modelRequestId;
        redisTemplate.delete(keyPrefix);
    }

    private static String getModelRequestPromptKey(Integer modelRequestId, String key) {
        return KEY_PREFIX + MODEL_REQUEST_KEY_PREFIX + modelRequestId +":" + key;
    }

    // -----------------------
    // 以下为每个常量的 getter 方法
    // -----------------------

    public String getDocCorrect() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT");
    }

    public String getDocCorrectContentTemplate() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE");
    }

    public String getDocCorrectV1() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_V1");
    }

    public String getDocCorrectContentTemplateV1() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE_V1");
    }

    public String getExtraQsTemplate() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "EXTRA_QS_TEMPLATE");
    }

    public String getExtraQsRespformatTemplete() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_TEMPLETE");
    }

    public String getExtraQsRespformatDoubaoSchema() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA");
    }

    public String getExtraQsRespformatSchema() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "EXTRA_QS_RESPFORMAT_SCHEMA");
    }

    public String getDocCorrectV2() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_V2");
    }

    public String getDocCorrectResponseFormat(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT");
    }

    public String getDocCorrectResponseFormatNormalQsFirstRequest(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST");
    }

    public String getDocCorrectResponseFormatTemplate(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE");
    }

    public String getDocCorrectResponseFormatSchema1(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1");
    }

    public String getDocCorrectResponseFormatSchema2(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2");
    }

    public String getDocCorrectResponseFormatSchema3(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3");
    }

    public String getDocCorrectResponseFormatSchema4(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4");
    }

    public String getDocCorrectResponseFormatSchema5(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5");
    }

    public String getDocCorrectWriteQsResponseFormat(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT");
    }

    public String getDocCorrectWriteQsResponseDoubaoFormat(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT");
    }

    public String getDocCorrectWriteQsResponseFormat2(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2");
    }

    public String getDocCorrectWriteQsResponseDoubaoFormat2(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2");
    }

    public String getDocCorrectWriteQsSystemPrompt(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT");
    }

    public String getDocCorrectWriteQsSystemPrompt2(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2");
    }

    public String getDocCorrectExtractStudentNamePrompt(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT");
    }

    public String getAutoByKey(Integer modelRequestId, String key) {
        if (Objects.isNull(modelRequestId)) {
            return redisTemplate.opsForValue().get(KEY_PREFIX + key);
        } else {
            String value = redisTemplate.opsForValue().get(getModelRequestPromptKey(modelRequestId, key));
            if (Objects.isNull(value)) {
                return redisTemplate.opsForValue().get(KEY_PREFIX + key);
            } else {
                return value;
            }
        }
    }

    public String getDocCorrectExtractStudentNameFormat(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT");
    }

    public String getDocCorrectExtractStudentNumberPrompt(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT");
    }

    public String getExtractPaperTopicPrompt() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_PROMPT");
    }

    public String getExtractPaperTopicJsonStructure() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE");
    }

    public String getDocCorrectExtractStudentNumberFormat(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT");
    }

    public String getDocCorrectScoreSystemPrompt(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_SCORE_SYSTEM_PROMPT");
    }

    public String getDocCorrectScoreResponseJSONPrompt() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT");
    }

    public String getDocCorrectScoreResponseFormat() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_SCORE_RESPONSE_FORMAT");
    }

    public String getDocCorrectEssayReportPrompt() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_PROMPT");
    }

    public String getDocCorrectEssayReportDoubaoPrompt() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT");
    }

    public String getDocCorrectEssayReportResponseFormat() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT");
    }

    public String getDoubaoCommonQuestionReturnStructure() {
        return redisTemplate.opsForValue().get(KEY_PREFIX + "Doubao_Common_Question_Return_Structure");
    }

//    public String getNormalQsFirstDefaultRequireJSONFormat() {
//        return redisTemplate.opsForValue().get(KEY_PREFIX + "Normal_Qs_First_Common_Question_Return_Structure");
//    }

    public String getCommonQuestionByKey(Integer modelRequestId, String key) {
        return getAutoByKey(modelRequestId, key);
    }

    public String getCommonQuestion(Integer modelRequestId, String commonQuestionType) {
        if (StrUtil.isBlank(commonQuestionType)) {
            return getDocCorrectResponseFormat(modelRequestId);
        }

        switch (commonQuestionType) {
            case "通用":
                return getDocCorrectResponseFormatTemplate(modelRequestId);
            case "单选题":
                return getCommonQuestionByKey(modelRequestId, "common_Question2");
            case "多选题":
                return getCommonQuestionByKey(modelRequestId, "common_Question3");
            case "判断题":
                return getCommonQuestionByKey(modelRequestId, "common_Question4");
            case "填空题":
                return getCommonQuestionByKey(modelRequestId, "common_Question5");
            case "简单的四则运算":
                return getCommonQuestionByKey(modelRequestId, "common_Question6");
            case "数学计算题":
                return getCommonQuestionByKey(modelRequestId, "common_Question7");
            case "数学应用题":
                return getCommonQuestionByKey(modelRequestId, "common_Question8");
            case "连线题":
                return getCommonQuestionByKey(modelRequestId, "common_Question9");
            case "画图题":
                return getCommonQuestionByKey(modelRequestId, "common_Question10");
            case "翻译题":
                return getCommonQuestionByKey(modelRequestId, "common_Question11");
            case "图表题":
                return getCommonQuestionByKey(modelRequestId, "common_Question12");
            case "涂卡题":
                return getCommonQuestionByKey(modelRequestId, "common_Question13");
            default:
                log.warn("未找到类型为'{}'的常见问题模板，返回默认模板", commonQuestionType);
                return getDocCorrectResponseFormatTemplate(modelRequestId);
        }
    }

    public String getCommonQuestionFirstRequest(Integer modelRequestId, String commonQuestionType) {
        if (StrUtil.isBlank(commonQuestionType)) {
            return getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId);
        }

        switch (commonQuestionType) {
            case "通用":
                return getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId);
            case "单选题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request2");
            case "多选题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request3");
            case "判断题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request4");
            case "填空题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request5");
            case "简单的四则运算":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request6");
            case "数学计算题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request7");
            case "数学应用题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request8");
            case "连线题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request9");
            case "画图题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request10");
            case "翻译题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request11");
            case "图表题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request12");
            case "涂卡题":
                return getCommonQuestionByKey(modelRequestId, "common_Question_first_request13");
            default:
                log.warn("未找到类型为'{}'的常见问题模板，返回默认模板", commonQuestionType);
                return getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId);
        }
    }

    public String getCommonQuestionSecondRequest(Integer modelRequestId) {
        return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST");
    }

    // -------------------------------
    // 修改与重置方法
    // -------------------------------

    /**
     * 修改指定完整 key 对应的 Redis 值为 newValue（覆盖原有值）
     *
     * @param completeKey 包含前缀的 key，如 "prompts:DOC_CORRECT"
     * @param newValue    新的内容
     */
    public void updateValue(String completeKey, String newValue) {
        try {
            String oldValue = redisTemplate.opsForValue().get(completeKey);

            PromptChangeLog log = new PromptChangeLog();
            log.setPromptKey(completeKey);
            log.setOldValue(oldValue);
            log.setNewValue(newValue);
            promptChangeLogService.save(log);
        } catch (Exception e) {
            log.error("更新prompt日志失败:e: {}", e.getMessage());
        }
        redisTemplate.opsForValue().set(completeKey, newValue);
    }

    /**
     * 重置指定完整 key 对应的 Redis 值为 PromptsConsts 中的原始内容
     *
     * @param completeKey 包含前缀的 key，如 "prompts:DOC_CORRECT"
     */
    public void resetValue(String completeKey) {
        // 如果 completeKey 包含前缀，则提取出 keySuffix
        String keySuffix = completeKey.startsWith(KEY_PREFIX) ? completeKey.substring(KEY_PREFIX.length()) : completeKey;
        String originalValue = originalConstants.get(keySuffix);
        try {
            if (originalValue != null) {
                String oldValue = redisTemplate.opsForValue().get(completeKey);
                PromptChangeLog log = new PromptChangeLog();
                log.setPromptKey(completeKey);
                log.setOldValue(oldValue);
                log.setNewValue(originalValue);
                promptChangeLogService.save(log);
            }
        } catch (Exception e) {
            log.error("重置操作 更新prompt日志失败:e: {}", e.getMessage());
        }
        if (originalValue != null) {
            redisTemplate.opsForValue().set(completeKey, originalValue);
        }
    }


    // -------------------------------
    // 返回所有 key 与对应中文名称的映射
    // -------------------------------
    public Map<String, Map<String, String>> getAllKeyListWithChineseNames() {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        for (String suffix : keysList) {
            String key   = KEY_PREFIX + suffix;
            String desc  = keyMap.getOrDefault(key, "");
            String type  = typeMap.getOrDefault(key, "");
            String value = redisTemplate.opsForValue().get(key);

            Map<String, String> meta = new HashMap<>(4);
            meta.put("key",   key);
            meta.put("desc",  desc);
            meta.put("type",  type);
            meta.put("value", value);

            result.put(key, meta);
        }
        return result;
    }

    /**
     * 列出所有通过 getAutoByKey 调用的 key，对应的中文名称和类型
     */
    public Map<String, Map<String, String>> listAutoByKeyNameAndType() {
        // 2. 取出所有 key 的完整信息（包括 desc 和 type）
        Map<String, Map<String, String>> allInfo = getAllKeyListWithChineseNames();

        // 3. 过滤出我们关心的那些 key，并只保留 desc 和 type
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        for (String suffix : keysList) {
            String completeKey = KEY_PREFIX + suffix;
            Map<String, String> meta = allInfo.get(completeKey);
            if (meta != null) {
                Map<String, String> info = new HashMap<>(2);
                info.put("name", meta.get("desc"));
                info.put("type", meta.get("type"));
                info.put("value", redisTemplate.opsForValue().get(completeKey));
                result.put(suffix, info);
            }
        }
        return result;
    }

    public Map<String, String> getAllKeyList() {
        Set<String> keys = redisTemplate.keys(KEY_PREFIX + "*");
        Map<String, String> result = new HashMap<>();
        if (keys != null && !keys.isEmpty()) {
            List<String> keyList = new ArrayList<>(keys);
            List<String> values = redisTemplate.opsForValue().multiGet(keyList);
            for (int i = 0; i < keyList.size(); i++) {
                String simpleKey = keyList.get(i).substring(KEY_PREFIX.length());
                result.put(simpleKey, values.get(i));
            }
        }
        return result;
    }



}

package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("model_request")
public class ModelRequest {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String name;

    private String modelValue;

    private Boolean jsonobject;

    private Boolean jsonschema;

    private Boolean enableNormalQsTwoRequest;


    private String content;
    private String remark;

    private Integer weight;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String prompt;
} 
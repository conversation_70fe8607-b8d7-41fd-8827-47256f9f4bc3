package com.chaty.completion;

import java.util.List;
import java.util.Map;

import com.chaty.dto.*;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.entity.OrgCorrectResult;
import com.chaty.form.ExtraQsForm;

import cn.hutool.json.JSONObject;

public interface CompletionService {

    void correctRecordArea(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity);

    void correctRecordAreaNormalQsTwoRequest(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity);

    List<CorrectQsDTO> extraQs(ExtraQsForm params);

    void correctAnswerCard(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity);

    /**
     * 写作题批改
     */
    Map<String, Object> correctWriteQs(String aimodel, ChatCompletionDTO completion, String taskId, GptAskLogEntity gptAskLogEntity);

    void correctWriteQsTwiceMergers(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity) throws InterruptedException;

    String createEssayAnalyticalReportCompletion(List<DocCorrectRecordDTO> records, DocCorrectRecordDTO param, String scoreSituation, String gradeName);

    /**
     * 老师分数识别
     */
    void correctScoreArea(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, GptAskLogEntity gptAskLogEntity) throws InterruptedException;

    OrgCorrectResult correctOrgQs(OrgQuestionDTO params);

    JSONObject getChatCompletionDTO(String recordId, Integer areaIdx, String modal);

    String extraStudentName(String imgUrl, String ocrText, String aimodel, GptAskLogEntity gptAskLogEntity, Integer modelRequestId);

    String extraStudentNumber(String imgUrl, String ocrText, String aimodel, GptAskLogEntity gptAskLogEntity, Integer modelRequestId);

    PaperTopicDTO extraPaperTopic(List<String> imgUrls, List<PaperTopicDTO> topics, String aimodel, GptAskLogEntity gptAskLogEntity);

    String getOcrContent(String service, String areaImg, String type) throws InterruptedException;
}
package com.chaty.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaty.common.BaseResponse;
import com.chaty.dto.FtpMessageDTO;
import com.chaty.dto.BindConfigPackageDTO;
import com.chaty.entity.FtpMessage;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.service.FtpMessageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/ftpMessage")
public class FtpMessageController {

    @Resource
    private FtpMessageService ftpMessageService;
    @Resource
    private FtpMessageMapper ftpMessageMapper;

    @PostMapping("/update")
    public BaseResponse<?> updateMessage(@RequestBody FtpMessageDTO dto) {
        FtpMessageDTO result = ftpMessageService.updateMessage(dto);
        return BaseResponse.ok(result);
    }

    @GetMapping("/delete")
    public BaseResponse<?> deleteMessage(@RequestParam Integer id) {
        ftpMessageService.deleteMessage(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/select")
    public BaseResponse<?> selectPage(@RequestBody FtpMessageDTO param) {
        IPage<FtpMessageDTO> page = ftpMessageService.selectPage(param);
        return BaseResponse.ok(page);
    }

    @GetMapping("/selectStatusCount")
    public BaseResponse<?> selectPage() {
        Map<String, Object> res = new HashMap<>();
        res.put("unFinishJobs", ftpMessageService.countUnFinishedUploadsBySchool());
        QueryWrapper<FtpMessage> wrapper = new QueryWrapper<>();

        long completed = ftpMessageMapper.selectCount(wrapper.eq("export_completed", true));
        long total = ftpMessageMapper.selectCount((null));
        res.put("inProgress", total - completed);
        res.put("completed", completed);
        res.put("totalQuantity", total);

        return BaseResponse.ok(res);
    }

    @GetMapping("/titles")
    public BaseResponse<?> titles(@RequestParam String title) {
        List<String> res = new ArrayList<>();


        QueryWrapper<FtpMessage> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT school_name")
                .like(StrUtil.isNotBlank(title),"school_name", title)
                .orderByDesc("school_name")
                .last("LIMIT 20");

        // 返回的是 Object 列表，需强转为 String
        List<Object> list = ftpMessageMapper.selectObjs(wrapper);
        if (list != null) {
            for (Object obj : list) {
                res.add((String) obj);
            }
        }

        return BaseResponse.ok(res);
    }

    /**
     * 查询相同主题的FtpMessage列表
     * @param configPackageId 配置包ID
     * @return 相同主题的FtpMessage列表
     */
    @GetMapping("/selectSameTopic")
    public BaseResponse<?> selectSameTopic(@RequestParam String configPackageId) {
        List<FtpMessageDTO> result = ftpMessageService.selectSameTopic(configPackageId);
        return BaseResponse.ok(result);
    }

    /**
     * 绑定配置包到FtpMessage列表
     * @param dto 包含FtpMessage ID列表和配置包ID的DTO
     * @return 绑定结果
     */
    @PostMapping("/bindConfigPackage")
    public BaseResponse<?> bindConfigPackage(@RequestBody BindConfigPackageDTO dto) {
        boolean result = ftpMessageService.bindConfigPackage(dto);
        return BaseResponse.ok(result ? "绑定成功" : "绑定失败");
    }
} 
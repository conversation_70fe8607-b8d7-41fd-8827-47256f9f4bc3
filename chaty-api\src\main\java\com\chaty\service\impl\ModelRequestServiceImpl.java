package com.chaty.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.ModelRequestDTO;
import com.chaty.entity.ModelRequest;
import com.chaty.exception.BaseException;
import com.chaty.mapper.ModelRequestMapper;
import com.chaty.service.DefaultModelRedisService;
import com.chaty.service.ModelRequestService;
import com.chaty.service.PromptsRedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Service
public class ModelRequestServiceImpl extends ServiceImpl<ModelRequestMapper, ModelRequest> implements ModelRequestService {

    @Resource
    private PromptsRedisService promptsRedisService;


    @Override
    public boolean addModelRequest(ModelRequestDTO modelRequestDTO) {
        ModelRequest modelRequest = new ModelRequest();
        BeanUtils.copyProperties(modelRequestDTO, modelRequest);
        modelRequest = initAllPrompt(modelRequest);
        promptsRedisService.setModelRequest(modelRequestDTO);
        return save(modelRequest);
    }

    public ModelRequest initAllPrompt(ModelRequest modelRequest) {
        Map<String, Map<String, String>>  data = promptsRedisService.listAutoByKeyNameAndType();
        JSONObject promptObj = new JSONObject();
        for (String key : data.keySet()) {
            Map<String, String> item = data.get(key);
            promptObj.set(key, item.getOrDefault("value", ""));
        }

        modelRequest.setPrompt(JSONUtil.toJsonStr(promptObj));
        return modelRequest;
    }

    @Override
    public boolean deleteModelRequest(Integer id) {
        promptsRedisService.deleteModelRequest(id);
        return removeById(id);
    }

    @Override
    public boolean updateModelRequest(ModelRequestDTO modelRequestDTO) {
        ModelRequest modelRequest = new ModelRequest();
        BeanUtils.copyProperties(modelRequestDTO, modelRequest);

        promptsRedisService.setModelRequest(modelRequestDTO);
        return updateById(modelRequest);
    }

    @Override
    public IPage<ModelRequestDTO> selectPage(ModelRequestDTO param) {
        LambdaQueryWrapper<ModelRequest> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(Objects.nonNull(param.getId()), ModelRequest::getId, param.getId());
        queryWrapper.like(StrUtil.isNotBlank(param.getName()), ModelRequest::getName, param.getName());
        queryWrapper.like(StrUtil.isNotBlank(param.getContent()), ModelRequest::getContent, param.getContent());
        queryWrapper.like(StrUtil.isNotBlank(param.getRemark()), ModelRequest::getRemark, param.getRemark());
        queryWrapper.eq(Objects.nonNull(param.getCreateTime()), ModelRequest::getCreateTime, param.getCreateTime());
        queryWrapper.eq(Objects.nonNull(param.getUpdateTime()), ModelRequest::getUpdateTime, param.getUpdateTime());
        queryWrapper.orderByDesc(ModelRequest::getWeight);
        queryWrapper.orderByDesc(ModelRequest::getCreateTime);

        if (param.getPage() == null) {
            throw new BaseException("Pagination parameters are required");
        }

        IPage<ModelRequest> modelRequestPage = page(param.getPage().page(ModelRequest.class), queryWrapper);

        return modelRequestPage.convert(entity -> {
            ModelRequestDTO dto = new ModelRequestDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });
    }

    @Override
    public int getMaxEmptyWeight() {
        // 按 weight 降序，只取一条记录
        LambdaQueryWrapper<ModelRequest> wrapper = Wrappers.<ModelRequest>lambdaQuery()
                .select(ModelRequest::getWeight)
                .orderByDesc(ModelRequest::getWeight)
                .last("LIMIT 1");
        ModelRequest top = getOne(wrapper);

        Integer maxWeight = (top != null && top.getWeight() != null)
                ? top.getWeight()
                : 0;
        return maxWeight + 1;
    }

} 
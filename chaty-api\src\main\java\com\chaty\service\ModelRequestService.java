package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.ModelRequestDTO;
import com.chaty.entity.ModelRequest;

public interface ModelRequestService extends IService<ModelRequest> {
    boolean addModelRequest(ModelRequestDTO modelRequestDTO);
    boolean deleteModelRequest(Integer id);
    boolean updateModelRequest(ModelRequestDTO modelRequestDTO);
    IPage<ModelRequestDTO> selectPage(ModelRequestDTO modelRequestDTO);

    int getMaxEmptyWeight();
} 
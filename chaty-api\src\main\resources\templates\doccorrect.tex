
\documentclass[12pt,landscape]{article}
\usepackage[paperwidth=${docInfo.width}mm, paperheight=${docInfo.height}mm]{geometry}

\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\usepackage{amssymb}
\usepackage{tikz}
\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}


% 定义自定义的勾（调整为更粗的线条）
\newcommand{\customCheck}[2]{
    \begin{tikzpicture}[scale=#1]
        \draw[line width=2pt, color=#2] (0,0.25) -- (0.125,0.125) -- (0.5,0.5);
    \end{tikzpicture}
}

% 定义自定义的叉
\newcommand{\customCross}[2]{
    \begin{tikzpicture}[scale=#1]
        \draw[line width=2pt, color=#2] (0,0) -- (0.5,0.5);
        \draw[line width=2pt, color=#2] (0.5,0) -- (0,0.5);
    \end{tikzpicture}
}

% 定义自定义的半对的勾
\newcommand{\halfCheck}[2]{
    \begin{tikzpicture}[scale=#1]
        % 画勾符号
        \draw[line width=2pt, color=#2] (0,0.25) -- (0.125,0.125) -- (0.5,0.5);
        % 画斜线，使其与勾的第二部分相交并与第一部分平行
        \draw[line width=2pt, color=#2] (0.25,0.5) -- (0.5,0.25);
    \end{tikzpicture}
}


\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}
\DeclareUnicodeCharacter{0964}{}

\begin{document}

    <#function latexEscape str>
        <#return str?replace("#", "\\#")?replace("$", "\\$")?replace("%", "\\%")?replace("&", "\\&")?replace("_", "+")?replace("{", "\\{")?replace("}", "\\}")?replace("~", "\\~")?replace("^", "\\^")?replace("\\", "\\textbackslash")?replace("[", "\\texttt{[}")?replace("]", "\\texttt{]}")>
    </#function>

    <#assign pageNo = 0>

    <#list records as record>
    <#if record.isRotate?? && record.isRotate == true>
        \pdfpageattr{/Rotate 180}
    <#else>
        \pdfpageattr{/Rotate 0}
    </#if>
    <#if pageNo == pageNum>
        <#assign pageNo = 0>
    </#if>
    <#assign pageNo = (pageNo + 1)>
    <#assign config = record.config>
    <#assign task = record.task>
    <#assign areas = config.getAreasObj()>
    <#assign configObj = config.getConfigObj()>
    <#assign fontSize = configObj.fontSize>
    <#assign flagSize = configObj.flagSize>
    <#assign flagColor = configObj.flagColor!'green'>
    <#assign errorFlagColor = configObj.errorFlagColor!'red'>
    <#assign errorFlagSize = (configObj.errorFlagSize)!configObj.flagSize>
    <#assign additionalName = (configObj.additionalName)!'附加'>
    <#assign isScore = configObj.score && configObj.scoreArea??>
    <#assign scoreArea = configObj.scoreArea>
    <#assign docurl = record.docurl>
    <#assign reviewedObj = record.getReviewedObj()>
    <#assign levelAreaX = record.totalScoreArea.x!1000>
    <#assign levelAreaY = record.totalScoreArea.y!50>
    <#assign levelRangeTxt = record.levelRangeTxt>
    <#assign isFirstPage = record.isFirstPage>
    <#assign firstPageScore = record.firstPageScore>
    <#assign scoreColor = record.scoreColor>


    % 分数
    <#if isScore && (pageNo == scorePageNo)>

            \begin{textblock}{1000}(${((levelAreaX + (record.offsetX!0)) / 300 * 25.4)},
                                    ${((levelAreaY + (record.offsetY!0)) / 300 * 25.4)})
                \fontsize{20pt}{100pt} \selectfont
                \begin{CJK*}{UTF8}{gbsn}
                    \textcolor{red}{${levelRangeTxt}}
                \end{CJK*}
            \end{textblock}

    </#if>

    <#if isScore && isFirstPage>
        <#assign areax = scoreArea.x!75>
        <#assign areay = scoreArea.y!150>
        \begin{textblock}{1000}(${((areax + (record.offsetX!0)) / 300 * 25.4)},
                                    ${((areay + (record.offsetY!0)) / 300 * 25.4)})
            \fontsize{20pt}{100pt} \selectfont
            \begin{CJK*}{UTF8}{gbsn}
                \textcolor{${scoreColor}}{${firstPageScore}}
            \end{CJK*}
        \end{textblock}
    </#if>

    % 区域
    <#list areas as area>
        <#if !(area.enabled?? && area.enabled == false)>
            <#assign questions = area.questions>
            <#assign areaType = area.areaType!1>
            <#assign qsArea = reviewedObj[area_index]>

            % 批改成功
            <#list questions as qs>
                <#if qsArea.reviewed?has_content && qs_index < qsArea.reviewed?size>
                    <#assign qsRes = qsArea.reviewed[qs_index]>
                    <#assign isTrue = qsRes.isCorrect?? && qsRes.isCorrect == 'Y'>
                    <#assign flagArea = qs.flagArea>
                    <#assign review = qsRes.review!''>
                    <#if (qs.reviewType!1) == 2>
                        <#assign review = qs.defaultReview>
                    </#if>

                    % 批改意见
                    <#if (!isTrue || areaType == 3) && review?? && qs.reviewArea??>
                        <#assign reviewArea = qs.reviewArea>
                        <#if areaType == 3>
                            \begin{textblock}{1000}(${((reviewArea.x + (record.offsetX!0)) / 300 * 25.4)},
                                                        ${((reviewArea.y + (record.offsetY!0)) / 300 * 25.4)})
                            \begin{minipage}{${(reviewArea.width / 300 * 25.4) - 20}mm}
                            \begin{CJK*}{UTF8}{gbsn}
                            \color{red}
                            \fontsize{${fontSize}}{15pt}\selectfont
                            评分: ${qsRes.scored!''} \\
                            <#if qsRes.review.错误分析??>
                            错误分析: \\
                            拼写错误: \\
                            ${latexEscape(qsRes.review.错误分析.拼写错误)!''} \\
                            语法错误:
                            ${latexEscape(qsRes.review.错误分析.语法错误)!''} \\
                            用词不当:
                            ${latexEscape(qsRes.review.错误分析.用词不当)!''} \\
                            </#if>
                            <#if qsRes.review.亮点分析??>
                            亮点分析: \\
                            高级词汇: \\
                            ${latexEscape(qsRes.review.亮点分析.高级词汇)!''} \\
                            亮点表达: \\
                            ${latexEscape(qsRes.review.亮点分析.亮点表达)!''} \\
                            </#if>
                            <#if qsRes.review.写作建议??>
                            写作建议: \\
                            ${latexEscape(qsRes.review.写作建议)!''} \\
                            </#if>
                            <#if qsRes.studentAnswer??>
                            学生作文全文: \\
                            ${latexEscape(qsRes.studentAnswer)!''} \\
                            </#if>
                            \end{CJK*}
                            \end{minipage}
                            \end{textblock}
                        <#else>
                            \begin{textblock}{1000}(${((reviewArea.x + (record.offsetX!0)) / 300 * 25.4)},
                                                        ${((reviewArea.y + (record.offsetY!0)) / 300 * 25.4)})
                            \fbox{
                            \pbox[t]{${(reviewArea.width / 300 * 25.4) - 20}mm}{
                            \begin{CJK*}{UTF8}{gbsn}
                            \textcolor{red}{\fontsize{${fontSize}}{50pt}\selectfont ${review}}
                            \end{CJK*}
                            }
                            }
                            \end{textblock}
                        </#if>
                    </#if>

                    % flag
                    \begin{textblock}{1000}(${((flagArea.x + (record.offsetX!0) + 27) / 300 * 25.4)},
                                                ${((flagArea.y + (record.offsetY!0) + 18) / 300 * 25.4)})
                    \begin{minipage}{30cm}
                        % 分数
                        <#assign qsScoredTxt = ''>
                        <#assign isBandui = false>
                        <#assign onlyShowWrong = onlyShowWrongQsScore!false>
                        <#assign tempShowQsScore = (!onlyShowWrong) || (!isTrue)>
                        <#if (showQsScore!false) && tempShowQsScore>
                            <#assign qsScored = 0>
                            <#if (qs.isScorePoint!1) == 2>
                                <#assign qsScored = qsRes.scored!"0">
                            <#elseif isTrue>
                                <#assign qsScored = qs.score>
                            </#if>
                            <#assign qsScoredTxt = ["\\fontsize{", configObj.scoreFontSize!10, "}{50pt}$", qsScored, "/", qs.score, "$"]?join("")>
                        </#if>
                        <#if qs.isScorePoint?number == 2>
                            <#assign qsScored = 0>
                            <#if (qs.isScorePoint!1) == 2>
                                <#assign qsScored = qsRes.scored!"0">
                            <#elseif isTrue>
                                <#assign qsScored = qs.score>
                            </#if>
                            <#assign qsScoredTxt = ["\\fontsize{", configObj.scoreFontSize!10, "}{50pt}$", qsScored, "/", qs.score, "$"]?join("")>
                                <#if qs.score != qsScored?number>
                                    <#assign isBandui = true>
                                    % 如果分数相同，画半对的符号
                                    \textcolor{${flagColor}}{%
                                      \fontsize{${flagSize}}{50pt}\selectfont
                                      \halfCheck{${flagSize/20}}{${flagColor}} ${qsScoredTxt}%
                                    }
                                </#if>

                        </#if>

                        <#if !isBandui>
                            <#if isTrue>
                                <#if ((configObj.correctFlag)!'a') == 'a'>
                                    \textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont $\checkmark$ ${qsScoredTxt}}
                                <#elseif configObj.correctFlag == 'b'>
                                    \textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \ding{51} ${qsScoredTxt}}
                                <#elseif configObj.correctFlag == 'c'>
                                    \textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \ding{52} ${qsScoredTxt}}
                                <#elseif configObj.correctFlag == 'd'>
                                    \textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \customCheck{${flagSize/20}}{${flagColor}} ${qsScoredTxt}}
                                </#if>
                            <#else>
                                <#if ((configObj.errorFlag)!'x') == 'x'>
                                    \textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{55} ${qsScoredTxt}}
                                <#elseif configObj.errorFlag == 'c'>
                                    \textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{56} ${qsScoredTxt}}
                                <#elseif configObj.errorFlag == 'd'>
                                    \textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{53} ${qsScoredTxt}}
                                <#elseif configObj.errorFlag == 'a'>
                                    \textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \customCross{${errorFlagSize/20*0.6}}{${errorFlagColor}} ${qsScoredTxt}}
                                <#elseif configObj.errorFlag == 'b'>
                                    \textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{54} ${qsScoredTxt}}
                                </#if>
                            </#if>
                        </#if>

                    \end{minipage}
                    \end{textblock}
                </#if>
            </#list>
        </#if>
    </#list>

    % 是否预览
    <#if isPreview>
        % 开启预览（底图部分保持原位，不加位移）
        \includepdf[pages=-, frame=true, scale=1, pagecommand={}, fitpaper=true]{${docurl?split("/")[2]}}
    <#else>
        \null
        \clearpage
    </#if>
</#list>

\end{document}

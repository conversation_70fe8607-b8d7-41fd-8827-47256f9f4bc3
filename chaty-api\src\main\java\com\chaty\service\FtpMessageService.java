package com.chaty.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.entity.FtpMessage;
import com.chaty.dto.FtpMessageDTO;
import com.chaty.dto.BindConfigPackageDTO;

import java.util.List;
import java.util.Map;

public interface FtpMessageService extends IService<FtpMessage> {
    
    FtpMessageDTO updateMessage(FtpMessageDTO dto);
    
    void deleteMessage(Integer id);

    IPage<FtpMessageDTO> selectPage(FtpMessageDTO param);


    Map<String, Long> countUnFinishedUploadsBySchool();

    /**
     * 根据configPackageId查询相同主题的FtpMessage列表
     * @param configPackageId 配置包ID
     * @return 相同主题的FtpMessage列表
     */
    List<FtpMessageDTO> selectSameTopic(String configPackageId);

    /**
     * 绑定配置包到FtpMessage列表
     * @param dto 包含FtpMessage ID列表和配置包ID的DTO
     * @return 绑定结果
     */
    boolean bindConfigPackage(BindConfigPackageDTO dto);

    /**
     * 根据ID获取FtpMessage对象
     * @param id FtpMessage ID
     * @return FtpMessage对象
     */
    FtpMessage getFtpMessageById(Integer id);

    PaperTopicDTO getTopic(FtpMessage message);

    void handleSamePaper(FtpMessage message, String sameToOthersPaperId);

    JSONObject downloadAndAttachPdfDetail(
            FtpMessage message,
            String parentPath
    );
}
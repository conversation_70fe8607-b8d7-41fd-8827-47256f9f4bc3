package com.chaty.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaty.common.BaseResponse;
import com.chaty.entity.ModelRequest;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CorrectConfigConsts;
import com.chaty.mapper.ModelRequestMapper;
import com.chaty.service.DefaultModelRedisService;
import com.chaty.service.PromptsRedisService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/defaultConfigs")
public class DefaultConfigsController {

    @Resource
    private PromptsRedisService promptsRedisService;
    @Resource
    private ModelRequestMapper modelRequestMapper;
    @Resource
    private DefaultModelRedisService defaultModelRedisService;

    @GetMapping("/getAll")
    public BaseResponse<?> getAll() {
        Map<String,Object> res = new HashMap<>();
        res.put("prompts", promptsRedisService.getAllKeyList());

        LambdaQueryWrapper<ModelRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(ModelRequest::getWeight);
        wrapper.orderByDesc(ModelRequest::getCreateTime);
        List<ModelRequest> list = modelRequestMapper.selectList(wrapper);
        Map<String, JSONObject> aimodelOptions = new LinkedHashMap<>();
        for (ModelRequest request : list) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.set("modelRequestId", request.getId());
                jsonObject.set("value", request.getModelValue());
                jsonObject.set("label", request.getName());
                jsonObject.set("responseFormat", CorrectConfigConsts.responseFormat);
                jsonObject.set("jsonobject", request.getJsonobject());
                jsonObject.set("jsonschema", request.getJsonschema());
                jsonObject.set("content", request.getContent());
                jsonObject.set("remark", request.getRemark());
                jsonObject.set("createTime", request.getCreateTime());
                aimodelOptions.put(request.getName(), jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        res.put("aimodelOptions", aimodelOptions);
        res.put("defaultCorrectModal", defaultModelRedisService.getName());
        return BaseResponse.ok("查询成功", res);
    }

    public static Map<String, String> getAllConstants(Class<?> clazz) {
        Map<String, String> constants = new HashMap<>();

        // 获取类中的所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // 判断是否是常量（public static final）
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                    java.lang.reflect.Modifier.isFinal(field.getModifiers()) &&
                    java.lang.reflect.Modifier.isPublic(field.getModifiers())) {
                try {
                    // 获取常量值，并将字段名称和值放入map中
                    Object value = field.get(null);  // 获取静态字段的值
                    constants.put(field.getName(), (String) value);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }

        return constants;
    }
}

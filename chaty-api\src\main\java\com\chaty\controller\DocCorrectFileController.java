package com.chaty.controller;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.entity.admin.ClassStatisticDataReq;
import com.chaty.exception.BaseException;
import com.chaty.mapper.*;
import com.chaty.service.*;
import com.chaty.task.correct.CorrectCacheService;
import com.chaty.task.correct.RecordCorrector;
import com.chaty.tenant.IgnoreTenant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.chaty.common.BaseResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/docCorrectFile")
public class DocCorrectFileController {

    @Resource
    private DocCorrectFileService docCorrectFileService;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;
    @Resource
    private FileService fileService;
    @Resource
    private DocCorrectTaskService docCorrectTaskService;
    @Resource
    private DocCorrectConfigService docCorrectConfigService;
    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private ClassMapper classMapper;
    @Autowired
    private CorrectCacheService correctCacheService;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private RecordCorrector recordCorrector;
    @Resource
    private SchoolMapper schoolMapper;

    @Value("${tenant.testAccount:83ad9e263e78e13613e0c2acc48ff5c8}")
    private String testAccountTenantId;
    @Autowired
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;
    @Autowired
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Autowired
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private ModelRequestMapper modelRequestMapper;

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody DocCorrectFileDTO param) {
        return BaseResponse.ok(docCorrectFileService.page(param));
    }

    @PostMapping("/create")
    public BaseResponse<?> create(@RequestBody DocCorrectFileDTO param) {
        DocCorrectFileDTO correctFile = docCorrectFileService.createFile(param, false);
        return BaseResponse.ok(correctFile);
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody DocCorrectFile param) {
        docCorrectFileService.updateById(param);
        return BaseResponse.ok("更新成功");
    }

    @PostMapping("/correct")
    public BaseResponse<?> correct(@RequestBody DocCorrectFileDTO param) {
        docCorrectFileService.doCorrect(param);
        return BaseResponse.ok("已提交批改");
    }

    @GetMapping("/checkName")
    public BaseResponse<?> checkName(@RequestParam String name) {
        QueryWrapper<DocCorrectFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        queryWrapper.eq("deleted", 0);
        List<DocCorrectFile> docCorrectFiles = docCorrectFileMapper.selectList(queryWrapper);
        if (!docCorrectFiles.isEmpty()) {
            return BaseResponse.error("文件名已存在");
        } else {
            return BaseResponse.ok("文件名可用");
        }
    }

    @PostMapping("/delete")
    public BaseResponse<?> delete(String id) {
        DocCorrectFile dbDocCorrectFile = docCorrectFileService.getById(id);
        if (Objects.nonNull(dbDocCorrectFile)) {
            // 批改中删除批改中的任务
            DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
            taskParam.setFileId(id);
            PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
            taskPageDTO.setPageNumber(-1);
            taskPageDTO.setPageSize(9999);
            taskPageDTO.setSearchCount(false);
            taskParam.setPage(taskPageDTO);
            taskParam.setOrderByName(true);
            IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
            List<DocCorrectTaskDTO> data = docCorrectTaskDTOIPage.getRecords();
            for (DocCorrectTaskDTO docCorrectTaskDTO : data) {
                recordCorrector.deleteCorrect(docCorrectTaskDTO.getId());
                docCorrectTaskService.delete(docCorrectTaskDTO.getId());
            }
        }
        docCorrectFileService.delete(id);
        return BaseResponse.ok("删除成功");
    }

    @GetMapping("/getById")
    public BaseResponse<?> getById(String id) {
        DocCorrectFileDTO res = docCorrectFileService.getById(id);
        return BaseResponse.ok(res);
    }


    @PostMapping("/classStatisticData")
    public BaseResponse<?> classStatisticData(@RequestBody ClassStatisticDataReq req) {

        JSONArray sheetData;
        if (req.getRanges() == null) {
            //没有等级数据
            sheetData = docCorrectFileService.getClassStatisticDataWithoutRanges(req);
        } else {
            //含有等级数据
            sheetData = docCorrectFileService.getClassStatisticData(req);
        }
        String url = "";
        try {
            url = docCorrectFileService.generateClassStatisticExcel(sheetData);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, Object> res = new HashMap<>();
        res.put("sheetData", sheetData);
        res.put("url", url);
        return BaseResponse.ok(res);
    }


    @PostMapping("/excelStyle4")
    public BaseResponse<?> excelStyle4(@RequestBody ClassStatisticDataReq req) {

        //考试人数，设置成班级内有多少份卷子（先不去做实际人数和实考人数，只保留字段实考人数改为考试人数）
        JSONArray originalData;
        if (req.getRanges() == null) {
            //没有等级数据
            originalData = docCorrectFileService.getClassStatisticDataWithoutRanges(req);
        } else {
            //含有等级数据
            originalData = docCorrectFileService.getClassStatisticData(req);
        }

        // Step 2: 处理班级统计表（只修改第一个sheet）
        JSONArray modifiedClassData = docCorrectFileService.processClassSheet(req, originalData);

        // Step 3: 重组完整数据（保留其他sheet）
        JSONArray finalData = new JSONArray();
        finalData.put(modifiedClassData);
        for (int i = 1; i < originalData.size(); i++) {
            finalData.put(originalData.get(i)); // 保留学生详情等后续sheet
        }

        String url = "";
        try {
            url = docCorrectFileService.generateClassStatisticExcel4(finalData);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, Object> res = new HashMap<>();
        res.put("sheetData", finalData);
        res.put("url", url);
        return BaseResponse.ok(res);
    }

    @PostMapping("/excelStyle5")
    public BaseResponse<?> excelStyle5(@RequestBody ClassStatisticDataReq req){
        // excelStyle5场景下，默认不包含等级数据
        JSONArray classStatisticData = docCorrectFileService.getClassStatisticDataWithoutRangesOnlyFirstSheet(req);

        String url = "";
        try {
            url = docCorrectFileService.generateClassStatisticExcel5(classStatisticData, req);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, Object> res = new HashMap<>();
        res.put("sheetData", classStatisticData);
        res.put("url", url);
        return BaseResponse.ok(res);
    }

    @PostMapping("/excelStyle6")
    public BaseResponse<?> excelStyle6(@RequestBody ClassStatisticDataReq req){

        // excelStyle6场景下，默认不包含等级数据
        JSONArray classStatisticData = docCorrectFileService.getClassStatisticDataForStyle6(req);

        String url = "";
        try {
            url = docCorrectFileService.generateClassStatisticExcel6(classStatisticData, req);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, Object> res = new HashMap<>();
        res.put("sheetData", classStatisticData);
        res.put("url", url);
        return BaseResponse.ok(res);
    }

    @GetMapping("/getDocCorrectFileListByConfigPackageId")
    public BaseResponse<?> getDocCorrectFileListByConfigPackageId(@RequestParam("id") String configPackageId) {
        List<DocCorrectFile> res = docCorrectFileService.getDocCorrectFileListByConfigPackageId(configPackageId);
        return BaseResponse.ok(res);
    }

    @Value("${essay.secret}")
    private String secret;

    @Value("${server.url}")
    private String serverUrl;
    @Value("${file.local.path}")
    private String localPath;

    /**
     * 写给别人用的api-写的比较仓促,发起批改
     *
     * @param file
     * @param apikey
     * @return 返回id
     */
    @PostMapping("/essayCorrect/start")
    @IgnoreTenant
    public BaseResponse<?> essayCorrect(@RequestParam(required = true) MultipartFile file,
                                        @RequestParam(required = true) String apikey,
                                        @RequestParam(required = true) String name) {
        if (!secret.equals(apikey)) {
            return BaseResponse.error("密钥错误");
        }
        JSONObject configObj = JSONUtil.parseObj("{\"name\":\"英语作文测试 统计结果\",\"pageNum\":1,\"modelValue\":\"gpt-4o-2024-08-06\"}");
        List<List<Map<String, Object>>> uploadRes = null;
        if (Objects.nonNull(file)) {
            uploadRes = fileService.uploadMulitePdf(file, configObj);
        }
        String url = (String) uploadRes.get(0).get(0).get("url");
        String paramStr = "{\"name\":\"英语作文测试 统计结果\",\"pageNum\":1,\"modelValue\":\"gpt-4o-2024-08-06\",\"pages\":[{\"isParse\":false,\"isRotation\":false,\"configId\":\"1899420503461400578\",\"configOptions\":[],\"configLoading\":false,\"docs\":[{\"url\":\"" + url + "\"}]}],\"essayData\":{\"name\":\"\",\"configId\":\"\",\"grade\":\"初一\",\"essayType\":\"unit\",\"questionType\":\"topic\",\"essayTitle\":\"\",\"scoringScheme\":\"score\",\"essayStyle\":\"englishEssay\",\"wordCount\":300,\"materials\":\"要求使用正确的句型，词汇准确，语法无误，段落结构清晰，内容与题目相关，并能够充分表达个人观点\",\"score\":100,\"isEnglishEssay\":true},\"isEssay\":1,\"configId\":\"1899420503461400578\",\"configName\":\"英语作文测试 统计结果_作文配置\",\"configOptions\":[{\"id\":\"1899420503461400578\",\"name\":\"英语作文测试 统计结果_作文配置\"}]}";
        JSONObject paramObj = JSONUtil.parseObj(paramStr);
        DocCorrectFileDTO param = JSONUtil.toBean(paramObj, DocCorrectFileDTO.class);
        if (!StrUtil.isBlank(name)) {
            param.setName(name);
        }
        DocCorrectFileDTO correctFile = docCorrectFileService.createFile(param, true);
        DocCorrectFileDTO correctParam = new DocCorrectFileDTO();
        correctParam.setId(correctFile.getId());
        docCorrectFileService.doCorrect(correctParam);
        return BaseResponse.ok(correctParam.getId());
    }

    /**
     * 查询进度
     *
     * @param apikey
     * @param id
     * @return
     */
    @GetMapping("/essayCorrect/query")
    @IgnoreTenant
    public BaseResponse<?> essayCorrect(@RequestParam(required = true) String apikey,
                                        @RequestParam(required = true) String id) {
        if (!secret.equals(apikey)) {
            return BaseResponse.error("密钥错误");
        }
        DocCorrectFileDTO res = docCorrectFileService.getById(id);
        if (res.getStatus() != 3) {
            return BaseResponse.error("批改中");
        }
        PageDTO<DocCorrectRecord> pageDTO = new PageDTO<>();
        pageDTO.setPageNumber(-1);
        pageDTO.setSearchCount(false);
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setSearchCount(false);
        // 查询task
        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        taskParam.setFileId(id);
        taskParam.setPage(taskPageDTO);
        IPage<DocCorrectTaskDTO> tasks = docCorrectTaskService.page(taskParam);
        // 查询record
        DocCorrectRecordDTO recordParam = new DocCorrectRecordDTO();
        recordParam.setTaskId(tasks.getRecords().get(0).getId());
        recordParam.setPage(pageDTO);
        IPage<DocCorrectRecordDTO> records = docCorrectRecordService.page(recordParam);
        // 生成报告
        DocCorrectRecordDTO param = new DocCorrectRecordDTO();
        param.setTaskId(tasks.getRecords().get(0).getId());
        Map<String, Object> reportResult = docCorrectRecordService.createEssayReport(param);
        String fileUrl = (String) reportResult.get("fileUrl");
        fileUrl = fileUrl.split("/static/")[1];
        // 最终结果
        Map<String, Object> result = new HashMap<>();
        String reportFileUrl = String.format("%s/api/docCorrectFile/essayCorrect/download?fileId=%s&apikey=your_apikey", serverUrl, fileUrl);
        result.put("reportFileUrl", reportFileUrl);
        result.put("fileId", fileUrl);
        result.put("paper", records);
        return BaseResponse.ok(result);
    }

    /**
     * 文件下载接口
     *
     * @param fileId 文件路径
     * @return 文件响应
     */
    @GetMapping("/essayCorrect/download")
    @IgnoreTenant
    public ResponseEntity<?> downloadFile(@RequestParam(required = true) String fileId, @RequestParam(required = true) String apikey) throws IOException {
        if (!secret.equals(apikey)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(BaseResponse.error("密钥错误"));
        }
        String fileName = fileId.substring(fileId.lastIndexOf("/") + 1);
        File file = new File(localPath + File.separator + fileName);
        if (!file.exists()) {
            return ResponseEntity.notFound().build();
        }
        FileSystemResource resource = new FileSystemResource(file);

        // 设置响应头，强制浏览器下载文件
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .body(resource);
    }


    @GetMapping("/getDeep")
    public BaseResponse<?> getDeep(@RequestParam String id) {
        if (StrUtil.isBlank(id)) {
            throw new BaseException("id不能为空");
        }
        JSONObject res = new JSONObject();
        Long rpm = 0L;
        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        taskParam.setFileId(id);
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setPageSize(9999);
        taskPageDTO.setSearchCount(false);
        taskParam.setPage(taskPageDTO);
        taskParam.setOrderByName(true);
        IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
        List<DocCorrectTaskDTO> data = docCorrectTaskDTOIPage.getRecords();
        for (DocCorrectTaskDTO docCorrectTaskDTO : data) {
            JSONObject detail = new JSONObject();
            DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTaskDTO.getConfigId());
            DocCorrectRecordDTO param = new DocCorrectRecordDTO();
            param.setTaskId(docCorrectTaskDTO.getId());
            PageDTO<DocCorrectRecord> page = new PageDTO<>();
            page.setPageNumber(-1);
            page.setPageSize(99999);
            page.setSearchCount(false);
            param.setPage(page);
            IPage<DocCorrectRecordDTO> recordDTOIPage = docCorrectRecordService.page(param);
            detail.set("config", config);
            detail.set("records", recordDTOIPage.getRecords());
            detail.set("task", docCorrectTaskDTO);
            detail.set("taskId", docCorrectTaskDTO.getId());
            res.put(docCorrectTaskDTO.getId(), detail);

            rpm += correctCacheService.getCurrentRpmForTask(docCorrectTaskDTO.getId());
        }
        DocCorrectFile docCorrectFile = docCorrectFileService.getById(id);
        if (StrUtil.isNotEmpty(docCorrectFile.getClassId())) {
            try {
                SchoolClass schoolClass = classMapper.selectById(docCorrectFile.getClassId());
                res.put("class", schoolClass);
                School school = schoolMapper.selectById(schoolClass.getSchoolId());
                res.set("school", school);
            }catch (Exception e) {
                log.error("获取班级信息失败: {}", e.getMessage());
            }
        }

        if (Objects.nonNull(data) && !data.isEmpty()) {
            try {
                if (Objects.nonNull(data.get(0).getModelRequestId())) {
                    ModelRequest modelRequest = modelRequestMapper.selectOne(new QueryWrapper<ModelRequest>().eq("id", data.get(0).getModelRequestId()));
                    res.set("modelDetail", modelRequest);
                }
            } catch (Exception e) {
                log.error("获取模型请求信息失败: {}", e.getMessage());
            }
        }

        res.set("fileDetail", docCorrectFile);
        res.set("tasks", data);
        res.set("rpm", rpm);
        return BaseResponse.ok(res);
    }

    @GetMapping("/getEssayDeep")
    public BaseResponse<?> getEssayDeep(@RequestParam String id) {
        if (StrUtil.isBlank(id)) {
            throw new BaseException("id不能为空");
        }
        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        taskParam.setFileId(id);
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setSearchCount(false);
        taskPageDTO.setPageSize(9999);
        taskParam.setPage(taskPageDTO);
        IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
        List<DocCorrectTaskDTO> data = docCorrectTaskDTOIPage.getRecords();
        if (data.isEmpty()) {
            throw new BaseException("无数据");
        }
        if (data.size() > 1) {
            throw new BaseException("数据异常-非作文");
        }
        DocCorrectTaskDTO docCorrectTaskDTO = data.get(0);
        String taskId = docCorrectTaskDTO.getId();

        JSONObject detail = new JSONObject();
        DocCorrectConfig config = docCorrectConfigService.getById(docCorrectTaskDTO.getConfigId());
        DocCorrectRecordDTO param = new DocCorrectRecordDTO();
        param.setTaskId(taskId);
        PageDTO<DocCorrectRecord> page = new PageDTO<>();
        page.setPageNumber(-1);
        page.setSearchCount(false);
        page.setPageSize(9999);
        param.setPage(page);
        IPage<DocCorrectRecordDTO> recordDTOIPage = docCorrectRecordService.page(param);
        detail.set("config", config);
        detail.set("records", recordDTOIPage.getRecords());
        detail.set("task", docCorrectTaskDTO);
        detail.set("taskId", taskId);

        return BaseResponse.ok(detail);
    }

    /**
     * 返回一个map，key是fileid,value是list<string>taskIds
     *
     * @param fileIds
     * @return
     */
    @PostMapping("/getTaskIdsByFileIds")
    public BaseResponse<?> getTaskIdsByFileIds(@RequestBody List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return BaseResponse.error("fileIds 不能为空");
        }

        // 查询所有 fileId 在 fileIds 列表内的任务记录
        QueryWrapper<DocCorrectTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("file_id", fileIds);
        List<DocCorrectTask> tasks = docCorrectTaskMapper.selectList(queryWrapper);

        // 按 fileId 分组，将每个任务的 id 收集到列表中
        Map<String, List<String>> taskIdsMap = tasks.stream()
                .collect(Collectors.groupingBy(
                        DocCorrectTask::getFileId,
                        Collectors.mapping(DocCorrectTask::getId, Collectors.toList())
                ));

        // 构造返回结果，每个 fileId 返回对应的文件信息和任务 id 列表
        Map<String, Object> result = new HashMap<>();
        for (String fileId : fileIds) {
            // 查询文件信息
            DocCorrectFile fileInfo = docCorrectFileMapper.selectById(fileId);
            // 获取对应的任务列表，没有则返回空列表
            List<String> taskIds = taskIdsMap.getOrDefault(fileId, new ArrayList<>());
            List<DocCorrectTask> tasksForFile = tasks.stream()
                    .filter(task -> fileId.equals(task.getFileId()))
                    .collect(Collectors.toList());
            // 计算额外的参数
            // 格式为: {docType或"8k"}_p{(记录数 * 任务数量)}
            String extraParam = "";
            if (!tasksForFile.isEmpty()) {
                // 使用该文件下第一个任务来计算
                DocCorrectTask firstTask = tasksForFile.get(0);

                // 查询该任务对应的配置信息
                DocCorrectConfig config = docCorrectConfigService.getById(firstTask.getConfigId());
                String docType = (config != null && config.getDocType() != null && !config.getDocType().isEmpty())
                        ? config.getDocType() : "8k";

                // 查询第一条任务的记录数
                QueryWrapper<DocCorrectRecord> recordQuery = new QueryWrapper<>();
                recordQuery.eq("task_id", firstTask.getId());
                int recordCount = (int) docCorrectRecordService.count(recordQuery);

                // 计算：记录数乘以该文件下任务数量
                int totalRecords = recordCount * tasksForFile.size();
                extraParam = docType + "_p" + totalRecords;
            }

            // 构造当前 fileId 对应的返回数据
            Map<String, Object> value = new HashMap<>();
            value.put("fileInfo", fileInfo);
            value.put("taskIds", taskIds);
            value.put("extraParam", extraParam);

            result.put(fileId, value);
        }

        return BaseResponse.ok(result);
    }


    @PostMapping("/adjust")
    public BaseResponse<?> adjust(@RequestBody DocCorrectRecordAdjustDTO param) {
        if (StrUtil.isBlank(param.getCurrentTaskId())) {
            return BaseResponse.error("当前任务id不能为空");
        }
        if (StrUtil.isBlank(param.getCurrentPageId())) {
            return BaseResponse.error("当前页码id不能为空");
        }
        if (StrUtil.isBlank(param.getFileId())) {
            return BaseResponse.error("文件id不能为空");
        }
        if (param.getSelectPageNumber() < 0) {
            return BaseResponse.error("页码不能小于1");
        }
        if (param.getSelectPageNumber() > 9999) {
            return BaseResponse.error("页码不能大于999");
        }
        param.setSelectedTaskIdx(param.getSelectedTaskIdx() - 1);
        param.setSelectPageNumber(param.getSelectPageNumber() - 1);
        // 先查看taskId是否变动
        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        taskParam.setFileId(param.getFileId());
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setPageSize(9999);
        taskPageDTO.setSearchCount(false);
        taskParam.setPage(taskPageDTO);
        taskParam.setOrderByName(true);
        IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
        List<DocCorrectTaskDTO> taskData = docCorrectTaskDTOIPage.getRecords();
        if (taskData.isEmpty()) {
            return BaseResponse.error("没有任务");
        }
        String selectTaskId = taskData.get(param.getSelectedTaskIdx()).getId();
        Boolean taskIdsHasChange = !selectTaskId.equals(param.getCurrentTaskId());
        // 先修改页码
        List<DocCorrectRecord> needChangeRecords = new ArrayList<>();
        DocCorrectRecordDTO recordParam = new DocCorrectRecordDTO();
        recordParam.setTaskId(selectTaskId);
        if (StrUtil.isBlank(recordParam.getTaskId())) {
            throw new BaseException("taskId不能为空");
        }
        PageDTO<DocCorrectRecord> page = new PageDTO<>();
        page.setPageNumber(-1);
        page.setPageSize(99999);
        page.setSearchCount(false);
        recordParam.setPage(page);
        IPage<DocCorrectRecordDTO> recordDTOIPage = docCorrectRecordService.page(recordParam);
        List<DocCorrectRecordDTO> selectAllRecordData = recordDTOIPage.getRecords();
        if (selectAllRecordData.isEmpty()) {
            throw new BaseException("被选中的面没有试卷");
        }
        String currentDocNameExample = selectAllRecordData.get(0).getDocname();
        DocCorrectRecord dbCurrentRecord = docCorrectRecordService.getById(param.getCurrentPageId());
        DocCorrectRecordDTO dbCurrentRecordDTO = new DocCorrectRecordDTO();
        dbCurrentRecordDTO.setDocname(selectAllRecordData.get(0).getDocname());
        BeanUtils.copyProperties(dbCurrentRecord, dbCurrentRecordDTO);
        if (taskIdsHasChange) {
            // 直接插入
            selectAllRecordData.add(param.getSelectPageNumber(), dbCurrentRecordDTO);
        } else {
            // 插入新的，移除旧的
            for (DocCorrectRecordDTO recordDTO : selectAllRecordData) {
                if (recordDTO.getId().equals(param.getCurrentPageId())) {
                    recordDTO.setIsOld(true);
                    break;
                }
            }
            selectAllRecordData.add(param.getSelectPageNumber(), dbCurrentRecordDTO);
            // 移除旧的
            selectAllRecordData.removeIf(DocCorrectRecordDTO::getIsOld);
        }

        // 调整顺序
        for (int i = 0; i < selectAllRecordData.size(); i++) {
            DocCorrectRecord record = new DocCorrectRecord();
            record.setId(selectAllRecordData.get(i).getId());
            String newDocname = adjustPageNumber(selectAllRecordData.get(i).getDocname(), i + 1);
            record.setDocname(newDocname);
            if (record.getId().equals(param.getCurrentPageId())) {
                // 需要给他换名字 & taskId
                record.setTaskId(selectTaskId);
                newDocname = adjustPageNumber(currentDocNameExample, i + 1);
                record.setDocname(newDocname);
            }
            needChangeRecords.add(record);
        }

        if (taskIdsHasChange) {
            // 还需要调整被移动的记录的taskId
            DocCorrectRecordDTO recordParamOtherTask = new DocCorrectRecordDTO();
            recordParamOtherTask.setTaskId(param.getCurrentTaskId());
            if (StrUtil.isBlank(recordParamOtherTask.getTaskId())) {
                throw new BaseException("taskId不能为空");
            }
            recordParamOtherTask.setPage(page);
            IPage<DocCorrectRecordDTO> recordDTOIOtherTaskPage = docCorrectRecordService.page(recordParamOtherTask);
            List<DocCorrectRecordDTO> otherTaskRecordData = recordDTOIOtherTaskPage.getRecords();
            // 移除掉旧的，重新计算页码
            otherTaskRecordData.removeIf(recordDTO -> recordDTO.getId().equals(param.getCurrentPageId()));
            for (int i = 0; i < otherTaskRecordData.size(); i++) {
                DocCorrectRecord record = new DocCorrectRecord();
                record.setId(otherTaskRecordData.get(i).getId());
                String newDocname = adjustPageNumber(otherTaskRecordData.get(i).getDocname(), i + 1);
                record.setDocname(newDocname);
                needChangeRecords.add(record);
            }
        }

        // 批量更新
        if (!needChangeRecords.isEmpty()) {
            docCorrectRecordService.updateBatchById(needChangeRecords);
        }
        return BaseResponse.ok("调整成功");
    }

    @GetMapping("/swap")
    public BaseResponse<?> adjust(@RequestParam("recordId") String recordId,
                                  @RequestParam("targetRecordId") String targetRecordId) {

        if (StrUtil.isBlank(recordId)) {
            return BaseResponse.error("当前recordId不能为空");
        }
        if (StrUtil.isBlank(targetRecordId)) {
            return BaseResponse.error("targetRecordId不能为空");
        }

        // 先交换，需要交换taskId和docName和docUrl
        DocCorrectRecord record1 = docCorrectRecordService.getById(recordId);
        DocCorrectRecord record2 = docCorrectRecordService.getById(targetRecordId);
//        String record2TaskId = record2.getTaskId();
//        String record2Docname = record2.getDocname();
        String record2Docurl = record2.getDocurl();

//        record2.setDocname(record1.getDocname());
        record2.setDocurl(record1.getDocurl());
//        record2.setTaskId(record1.getTaskId());

//        record1.setTaskId(record2TaskId);
//        record1.setDocname(record2Docname);
        record1.setDocurl(record2Docurl);

        if (StrUtil.isBlank(record2.getSwapTargetRecordId()) && StrUtil.isBlank(record1.getSwapTargetRecordId())) {
            record1.setSwapTargetRecordId(record2.getId());
            record2.setSwapTargetRecordId(record1.getId());
        } else {
            record1.setSwapTargetRecordId("");
            record2.setSwapTargetRecordId("");
        }

        docCorrectRecordService.updateById(record1);
        docCorrectRecordService.updateById(record2);
        return BaseResponse.ok("调整成功");
    }


    public static String adjustPageNumber(String docname, int pagenumber) {
        // Split the document name by "_"
        String[] splitResult = docname.split("_");

        // Format the specified page number to 3 digits (with leading zeros)
        String formattedPageNumber = String.format("%03d", pagenumber);

        // Replace the last part with the specified page number
        splitResult[splitResult.length - 1] = formattedPageNumber;

        // Rebuild the modified document name
        return String.join("_", splitResult);
    }


    public static String incrementPageNumber(String docname) {
        // Split the document name by "_"
        String[] splitResult = docname.split("_");

        // Get the last part (page number), increment it
        String pageNumber = splitResult[splitResult.length - 1];
        int pageNumberInt = Integer.parseInt(pageNumber);
        pageNumberInt += 1;

        // Format the page number to retain leading zeros (3 digits)
        String incrementedPageNumber = String.format("%03d", pageNumberInt);

        // Replace the last part with the incremented page number
        splitResult[splitResult.length - 1] = incrementedPageNumber;

        // Rebuild the modified document name
        return String.join("_", splitResult);
    }

    @IgnoreTenant
    @GetMapping("/copy2testAccount")
    public BaseResponse<?> copy2testAccount(@RequestParam String fileId) {
        if (StrUtil.isBlank(fileId)) {
            return BaseResponse.error("fileId不能为空");
        }
        DocCorrectFile file = docCorrectFileMapper.selectById(fileId);
        if (file == null) {
            return BaseResponse.error("文件不存在");
        }
        List<DocCorrectTask> docCorrectTasks = docCorrectTaskMapper.selectList(new QueryWrapper<DocCorrectTask>().eq("file_id", fileId));
        List<List<DocCorrectRecord>> docCorrectRecords = new ArrayList<>();
        for (DocCorrectTask docCorrectTask : docCorrectTasks) {
            List<DocCorrectRecord> records = docCorrectRecordService.list(new QueryWrapper<DocCorrectRecord>().eq("task_id", docCorrectTask.getId()));
            docCorrectRecords.add(records);
        }
        String configPackageId = file.getConfigPackageId();
        if (StrUtil.isBlank(configPackageId)) {
            return BaseResponse.error("文件配置包id不能为空");
        }
        DocCorrectConfigPackage configPackage = docCorrectConfigPackageMapper.selectById(configPackageId);
        if (configPackage == null) {
            return BaseResponse.error("配置包不存在");
        }
        // 绑定的configs
        String configIds = configPackage.getConfig();
        List<String> configIdList = JSONUtil.toList(configIds, String.class);
        if (configIdList.isEmpty()) {
            return BaseResponse.error("配置包没有绑定配置");
        }
        List<DocCorrectConfig> configs = docCorrectConfigService.list(new QueryWrapper<DocCorrectConfig>().in("id", configIdList));

        // 开始执行复制，先 create config then config package then create file then create tasks then create records, tennatId都改为testAccountTenantId
        // —— 开始执行复制 —— //

        // 1. 复制 Config（先于 Package）
        List<String> newConfigIds = new ArrayList<>();
        for (DocCorrectConfig origConfig : configs) {
            DocCorrectConfig newConfig = new DocCorrectConfig();
            BeanUtil.copyProperties(origConfig, newConfig,
                    CopyOptions.create()
                            .ignoreNullValue()
                            .ignoreError()
                            .setIgnoreProperties("id", "createTime", "updateTime")
            );
            newConfig.setId(IdUtil.simpleUUID());
            newConfig.setTenantId(testAccountTenantId);
            docCorrectConfigMapper.insert(newConfig);
            newConfigIds.add(newConfig.getId());
        }

        // 2. 创建新的 ConfigPackage
        DocCorrectConfigPackage newPackage = new DocCorrectConfigPackage();
        BeanUtil.copyProperties(configPackage, newPackage,
                CopyOptions.create()
                        .ignoreNullValue()
                        .ignoreError()
                        .setIgnoreProperties("id", "createTime", "updateTime")
        );
        newPackage.setId(IdUtil.simpleUUID());
        newPackage.setTenantId(testAccountTenantId);
        // 使用刚复制的 newConfigIds 绑定到新包
        newPackage.setConfig(JSONUtil.toJsonStr(newConfigIds));
        newPackage.setName(newPackage.getName() + "-复制的测试配置");
        docCorrectConfigPackageMapper.insert(newPackage);
        String newPackageId = newPackage.getId();

        // 3. 复制 File 并关联到新 Package
        DocCorrectFile newFile = new DocCorrectFile();
        BeanUtil.copyProperties(file, newFile,
                CopyOptions.create()
                        .ignoreNullValue()
                        .ignoreError()
                        .setIgnoreProperties("id", "createTime", "updateTime")
        );
        newFile.setConfigPackageId(newPackageId);
        newFile.setTenantId(testAccountTenantId);
        newFile.setId(IdUtil.simpleUUID());
        newFile.setName(newFile.getName() + "-复制的测试试卷");
        docCorrectFileMapper.insert(newFile);

        // 4. 复制 Task 并关联到新 File
        List<DocCorrectTask> newTasks = new ArrayList<>();
        int index = 0;
        for (DocCorrectTask origTask : docCorrectTasks) {
            DocCorrectTask newTask = new DocCorrectTask();
            BeanUtil.copyProperties(origTask, newTask,
                    CopyOptions.create()
                            .ignoreNullValue()
                            .ignoreError()
                            .setIgnoreProperties("id", "createTime", "updateTime")
            );
            newTask.setFileId(newFile.getId());
            newTask.setTenantId(testAccountTenantId);
            newTask.setId(IdUtil.simpleUUID());
            newTask.setConfigId(newConfigIds.get(index));
            docCorrectTaskMapper.insert(newTask);
            newTasks.add(newTask);
            index++;
        }

        // 5. 复制 Record 并关联到对应的新 Task
        for (int i = 0; i < newTasks.size(); i++) {
            DocCorrectTask newTask = newTasks.get(i);
            List<DocCorrectRecord> origRecs = docCorrectRecords.get(i);
            for (DocCorrectRecord origRec : origRecs) {
                DocCorrectRecord newRec = new DocCorrectRecord();
                BeanUtil.copyProperties(origRec, newRec,
                        CopyOptions.create()
                                .ignoreNullValue()
                                .ignoreError()
                                .setIgnoreProperties("id", "createTime", "updateTime")
                );
                newRec.setTaskId(newTask.getId());
                newRec.setTenantId(testAccountTenantId);
                newRec.setId(IdUtil.simpleUUID());
                newRec.setConfigId(newTask.getConfigId());
                docCorrectRecordMapper.insert(newRec);
            }
        }


        // 返回新复制的文件对象
        return BaseResponse.ok(newFile);
    }

    /**
     * 增加 一键重测 按钮，可以重新选择模型、输入试卷名称 -> 复制试卷- > 发起批改。留痕批改/时间批改 都增加
     * @return
     */
    @PostMapping("/oneClickRetest")
    public BaseResponse<?> oneClickRetest(@RequestBody List<OneClickRetestDTO> req) {
        List<DocCorrectTaskDTO> newTasks = new ArrayList<>();

        for (OneClickRetestDTO reqDTO : req) {
            try {
                List<DocCorrectTaskDTO> tasks = docCorrectFileService.oneClickRetest(reqDTO);
                newTasks.addAll(tasks);
            }catch (RuntimeException e) {
                return BaseResponse.error(e.getMessage());
            }
        }

        docCorrectTaskService.executeBatch(newTasks);
        // 返回新复制的文件对象
        return BaseResponse.ok("批量重测成功！");
    }
}

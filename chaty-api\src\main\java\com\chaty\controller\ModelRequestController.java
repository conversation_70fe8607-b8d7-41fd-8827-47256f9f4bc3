package com.chaty.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.ModelRequestDTO;
import com.chaty.entity.ModelRequest;
import com.chaty.entity.PromptReq;
import com.chaty.service.DefaultModelRedisService;
import com.chaty.service.ModelRequestService;
import com.chaty.service.PromptsRedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/model-request")
public class ModelRequestController {

    @Autowired
    private ModelRequestService modelRequestService;

    @Autowired
    private DefaultModelRedisService defaultModelRedisService;
    @Autowired
    private PromptsRedisService promptsRedisService;

    @PostMapping("/add")
    public BaseResponse<?> addModelRequest(@RequestBody ModelRequestDTO modelRequestDTO) {
        try {
            modelRequestService.addModelRequest(modelRequestDTO);
        } catch (Exception e) {
            return BaseResponse.error("添加模型失败" );
        }
        return BaseResponse.ok("添加成功");
    }

    @GetMapping("/delete")
    public BaseResponse<?> deleteModelRequest(@RequestParam Integer id) {
        modelRequestService.deleteModelRequest(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> updateModelRequest(@RequestBody ModelRequestDTO modelRequestDTO) {
        try {
            modelRequestService.updateModelRequest(modelRequestDTO);
        } catch (Exception e) {
            return BaseResponse.error("更新模型失败");
        }
        return BaseResponse.ok("更新成功");
    }

    @PostMapping("/addPrompt")
    public BaseResponse<?> addPromptModelRequest(@RequestBody PromptReq promptReq) {
        ModelRequest dbModelRequest = modelRequestService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelRequest)) {
            return BaseResponse.error("找不到该模型");
        }
        ModelRequestDTO dbModelRequestDTO = new ModelRequestDTO();
        BeanUtils.copyProperties(dbModelRequest, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型已存在该key");
        }
        promptObj.put(promptReq.getKey(), promptReq.getValue());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));
        modelRequestService.updateModelRequest(dbModelRequestDTO);
        return BaseResponse.ok("添加成功");
    }

    /**
     * 更新模型请求中的提示信息
     */
    @PostMapping("/updatePrompt")
    public BaseResponse<?> updatePrompt(@RequestBody PromptReq promptReq) {
        ModelRequest dbModelRequest = modelRequestService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelRequest)) {
            return BaseResponse.error("找不到该模型");
        }

        ModelRequestDTO dbModelRequestDTO = new ModelRequestDTO();
        BeanUtils.copyProperties(dbModelRequest, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (!promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型不存在该key");
        }

        promptObj.put(promptReq.getKey(), promptReq.getValue());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));

        // 更新实体对象
        BeanUtils.copyProperties(dbModelRequestDTO, dbModelRequest);
        modelRequestService.updateModelRequest(dbModelRequestDTO);

        return BaseResponse.ok("更新成功");
    }

    /**
     * 删除模型请求中的提示信息
     */
    @PostMapping("/deletePrompt")
    public BaseResponse<?> deletePrompt(@RequestBody PromptReq promptReq) {
        ModelRequest dbModelRequest = modelRequestService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelRequest)) {
            return BaseResponse.error("找不到该模型");
        }

        ModelRequestDTO dbModelRequestDTO = new ModelRequestDTO();
        BeanUtils.copyProperties(dbModelRequest, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (!promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型不存在该key");
        }

        promptObj.remove(promptReq.getKey());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));

        // 更新实体对象
        BeanUtils.copyProperties(dbModelRequestDTO, dbModelRequest);
        modelRequestService.updateModelRequest(dbModelRequestDTO);

        return BaseResponse.ok("删除成功");
    }

    /**
     * 列出所有前端模型仓库可以设置的key
     * @return
     */
    @GetMapping("/getAllPromptKey")
    public BaseResponse<?> getAllPromptKey() {
        return BaseResponse.ok(promptsRedisService.listAutoByKeyNameAndType());
    }

    @PostMapping("/selectPage")
    public BaseResponse<IPage<ModelRequestDTO>> selectPage(@RequestBody ModelRequestDTO modelRequestDTO) {
        IPage<ModelRequestDTO> result = modelRequestService.selectPage(modelRequestDTO);
        return BaseResponse.ok(result);
    }

    @GetMapping("/getMaxEmptyWeight")
    public BaseResponse<Integer> getMaxEmptyWeight() {
        return BaseResponse.ok(modelRequestService.getMaxEmptyWeight());
    }

    // -----------------------
    // 默认模型配置相关接口
    // -----------------------

    /**
     * 获取当前默认模型配置信息
     */
    @GetMapping("/default/get")
    public BaseResponse<ModelRequest> getDefaultModel() {
        ModelRequest req = defaultModelRedisService.getModelRequest();
        return BaseResponse.ok(req);
    }

    /**
     * 更新默认模型配置：根据传入的 ModelRequest ID
     */
    @PostMapping("/default/update/{id}")
    public BaseResponse<?> updateDefaultModel(@PathVariable Integer id) {
        ModelRequest req = modelRequestService.getById(id);
        if (req == null) {
            return BaseResponse.error("未找到对应的模型请求");
        }
        defaultModelRedisService.update(req);
        return BaseResponse.ok("默认模型已更新");
    }

    /**
     * 重置默认模型所有配置为初始值
     */
    @PostMapping("/default/reset")
    public BaseResponse<?> resetDefaultModel() {
        // 重置所有字段
        Map<String, String> defaults = defaultModelRedisService.getAllDefaultConfig();
        defaults.forEach((suffix, val) ->
                defaultModelRedisService.resetValue("defaultModel:" + suffix)
        );
        return BaseResponse.ok("默认模型已重置到初始值");
    }
}

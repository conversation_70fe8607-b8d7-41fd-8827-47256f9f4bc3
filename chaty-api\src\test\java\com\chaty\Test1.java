package com.chaty;


import java.util.List;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.api.essayScoring.EssayScoreApi;
import com.chaty.api.essayScoring.EssayScoreBaseResponse;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.PageDTO;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.service.DocCorrectRecordService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import com.chaty.util.PDFUtil;

import cn.hutool.core.util.EscapeUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.Resource;

import static com.chaty.util.HtmlTemplateProcessor.convertHtmlToPdf;

@Slf4j
@SpringBootTest
public class Test1 {
    @Resource
    private EssayScoreApi essayScoreApi;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;

    private static void main(String[] afgs) {

    }



    @Test
    public void refreshHasChange() {
        log.info("开始执行 refreshHasChange 测试");

        DocCorrectRecordDTO dto = new DocCorrectRecordDTO();
        PageDTO<DocCorrectRecord> pageDTO = new PageDTO<>();
        int pageNumber = 1;
        pageDTO.setPageNumber(pageNumber);
        pageDTO.setPageSize(10);
        pageDTO.setSearchCount(true);
        dto.setHasChange(1);
        dto.setOrderByTaskId(false);
        dto.setPage(pageDTO);
        dto.setDocname("数学");

        long total = Long.MAX_VALUE;  // 初始设置为无穷大，首次进入循环
        int updatedCount = 0;

        while ((pageNumber - 1) * 10L < total) {
            log.info("第 {} 页开始读取，pageSize=500", pageNumber);
            IPage<DocCorrectRecordDTO> res = docCorrectRecordService.page(dto);
            total = res.getTotal();
            log.info("本次查询共 {} 条记录，总记录数 {}，已查询 {}", res.getRecords().size(), total, (pageNumber - 1) * 10L);

            for (DocCorrectRecordDTO item : res.getRecords()) {
                // 只对 hasChange=1 的记录进一步检查
                if (item.getHasChange() != null && item.getHasChange() == 1) {
                    log.debug("检查记录 id={} 的 reviewedObj", item.getId());

                    // 遍历所有题目，判断是否仍有 hasChange=1
                    int hasChangeFlag = 0;
                    JSONArray areas = item.getReviewedObj();
                    outer:
                    for (int i = 0; i < areas.size(); i++) {
                        JSONObject areaObj = areas.getJSONObject(i);
                        JSONArray questions = areaObj.getJSONArray("reviewed");
                        for (int j = 0; j < questions.size(); j++) {
                            JSONObject qObj = questions.getJSONObject(j);
                            if (qObj.containsKey("hasChange") && "1".equals(qObj.getStr("hasChange"))) {
                                hasChangeFlag = 1;
                                break outer;
                            }
                        }
                    }

                    if (hasChangeFlag == 0) {
                        log.info("记录 id={} 下所有题目均没有变更，准备将 hasChange 置 0 并更新", item.getId());
                        DocCorrectRecordDTO updateDto = new DocCorrectRecordDTO();
                        updateDto.setId(item.getId());
                        updateDto.setHasChange(0);
                        docCorrectRecordService.updateById(updateDto);
                        updatedCount++;
                        log.info("记录 id={} 更新成功（hasChange=0）", item.getId());
                    } else {
                        log.debug("记录 id={} 仍有题目变更，跳过修改", item.getId());
                    }
                } else {
                    log.debug("记录 id={} hasChange != 1，跳过检查", item.getId());
                }
            }

            pageNumber++;
            dto.getPage().setPageNumber(pageNumber);

            log.info("一次查询循环，共更新 {} 条记录", updatedCount);
        }

        log.info("refreshHasChange 执行完毕，共更新 {} 条记录", updatedCount);
    }



    //    public static void main(String args[]) {
//        String test = "{\"第一步识别图片中的所有文字和图片内容\": {\"图片中的内容\": [], \"图片中的文字\": [\"2又2/3×9/16 - 6/17÷9/34\", \"=8/3 + 4/3\", \"=41/24\", \"2.75×4又1/5 + 5.8÷4/11\", \"=2.75×4又1/5 + 5.8×11/4\", \"=1155 + 159.5\", \"=2314.5\"]}, \"第三步识别学生答案\": {\"题目1的学生答案\": \"41/24\", \"题目2的学生答案\": \"2314.5\"}, \"第二步识别题干和完整学生回答内容\": {\"题目1\": {\"题目1的完整学生回答内容\": \"2又2/3×9/16 - 6/17÷9/34 =8/3 + 4/3 =41/24\", \"题目1的题干\": \"计算2又2/3×9/16 - 6/17÷9/34 \"}, \"题目2\": {\"题目2的完整学生回答内容\": \"2.75×4又1/5 + 5.8÷4/11 =2.75×4又1/5 + 5.8×11/4 =1155 + 159.5 =2314.5\", \"题目2的题干\": \"计算2.75×4又1/5 + 5.8÷4/11 \"}}, \"第五步具体批改\": {\"题目1\": {\"学生答案\": \"41/24\", \"得分\": 1/6, \"是否正确\": true, \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个分数,例如'1/2'\", \"评价\": \"答案正确，计算过程清晰\"}, \"题目2\": {\"学生答案\": \"2314.5\", \"得分\": 0, \"是否正确\": false, \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个一位小数,例如'3.5'\", \"评价\": \"答案错误，计算过程有误\"}}, \"第四步根据正确答案判断学生答案是否正确\": {\"题目1\": {\"判断理由\": \"学生答案为41/24，符合题目要求的分数形式\", \"判断结果\": true, \"学生答案\": \"41/24\", \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个分数,例如'1/2'\"}, \"题目2\": {\"判断理由\": \"学生答案为2314.5，不是一位小数，不符合题目要求\", \"判断结果\": false, \"学生答案\": \"2314.5\", \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个一位小数,例如'3.5'\"}}}";
//        // 使用 Hutool JSONUtil 解析
//        JSONObject res = JSONUtil.parseObj(test);
//        System.out.println(res);
//    }
//    public static void main(String args[]) {
//        String test = " {\"第一步识别图片中的所有文字和图片内容\": {\"图片中的内容\": [], \"图片中的文字\": [\"6:4=3:( 2 ),\", \"1/5:( 8/15 )=1 7/8:5,\", \"3.75/( 5 )=3/4,\", \"0.3:(0.015)=7:0.35\"]}, \"第三步识别学生答案\": {\"题目1的学生答案\": \"2\", \"题目2的学生答案\": \"8/15\", \"题目3的学生答案\": \"5\", \"题目4的学生答案\": \"0.015\"}, \"第二步识别题干和完整学生回答内容\": {\"题目1\": {\"题目1的完整学生回答内容\": \"6:4=3:( 2 )\", \"题目1的题干\": \"题目:1 \\n 答案位置:6 : 4 = 3 : (  ) \\n 正确答案:从上往下数第一根横线上是学生答案,学生答案为一个数字 \\n 题目满分:2\"}, \"题目2\": {\"题目2的完整学生回答内容\": \"1/5:( 8/15 )=1 7/8:5\", \"题目2的题干\": \"题目:2 \\n 答案位置:1 / 5 : (  ) = 1 7 / 8 : 5 \\n 正确答案:从上往下数第二根横线上是学生答案,学生答案为一个分数 \\n 题目满分:8/15\"}, \"题目3\": {\"题目3的完整学生回答内容\": \"3.75/( 5 )=3/4\", \"题目3的题干\": \"题目:3 \\n 答案位置:3.75 / (  ) = 3 / 4 \\n 正确答案:从上往下数第三根横线上是学生答案,学生答案为一个数字 \\n 题目满分:5\"}, \"题目4\": {\"题目4的完整学生回答内容\": \"0.3:(0.015)=7:0.35\", \"题目4的题干\": \"题目:4 \\n 答案位置:0.3 : (  ) = 7 : 0.35 \\n 正确答案:从上往下数第四根横线上是学生答案,学生答案为一个小数 \\n 题目满分:0.015\"}}, \"第五步具体批改\": {\"题目1\": {\"学生答案\": \"2\", \"得分\": 2, \"是否正确\": true, \"正确答案\": \"2\", \"评价\": \"答案正确，计算准确。\"}, \"题目2\": {\"学生答案\": \"8/15\", \"得分\": 8/15, \"是否正确\": true, \"正确答案\": \"8/15\", \"评价\": \"答案正确，比例计算正确。\"}, \"题目3\": {\"学生答案\": \"5\", \"得分\": 5, \"是否正确\": true, \"正确答案\": \"5\", \"评价\": \"答案正确，计算无误。\"}, \"题目4\": {\"学生答案\": \"0.015\", \"得分\": 0.015, \"是否正确\": true, \"正确答案\": \"0.015\", \"评价\": \"答案正确，小数计算准确。\"}}, \"第四步根据正确答案判断学生答案是否正确\": {\"题目1\": {\"判断理由\": \"学生答案为2，与正确答案一致。\", \"判断结果\": true, \"学生答案\": \"2\", \"正确答案\": \"2\"}, \"题目2\": {\"判断理由\": \"学生答案为8/15，与正确答案一致。\", \"判断结果\": true, \"学生答案\": \"8/15\", \"正确答案\": \"8/15\"}, \"题目3\": {\"判断理由\": \"学生答案为5，与正确答案一致。\", \"判断结果\": true, \"学生答案\": \"5\", \"正确答案\": \"5\"}, \"题目4\": {\"判断理由\": \"学生答案为0.015，与正确答案一致。\", \"判断结果\": true, \"学生答案\": \"0.015\", \"正确答案\": \"0.015\"}}}]";
//        String fifthStepJson = extractFifthStep(test);
//        System.out.println(fifthStepJson);
//        fifthStepJson = "{\"第五步具体批改\": {\"题目1\": {\"学生答案\": \"41/24\", \"得分\": 1/6, \"是否正确\": true, \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个分数,例如'1/2'\", \"评价\": \"答案正确，计算过程清晰\"}, \"题目2\": {\"学生答案\": \"2314.5\", \"得分\": 0, \"是否正确\": false, \"正确答案\": \"整道计算题的结果为学生答案,学生答案为一个一位小数,例如'3.5'\", \"评价\": \"答案错误，计算过程有误\"}}}";
//        JSONObject res = JSONUtil.parseObj(fifthStepJson);
//        System.out.println(res);
//    }

    /**
     * 提取 JSON 字符串中 "第五步具体批改" 的部分
     *
     * @param input 原始 JSON 字符串
     * @return 提取的 "第五步具体批改" 部分 JSON 字符串
     */
    public static String extractFifthStep(String input) {
        // 定义"第五步具体批改"的关键字
        String startKeyword = "第五步具体批改";

        // 定位到 "第五步具体批改" 的开始位置
        int startIndex = input.indexOf(startKeyword);

        if (startIndex == -1) {
            return null; // 如果没有找到"第五步具体批改"，返回null
        }

        // 找到开始位置后，继续搜索匹配的第一个 { 字符
        int startBraceIndex = input.indexOf("{", startIndex + startKeyword.length());

        if (startBraceIndex == -1) {
            return null; // 如果没有找到 {，返回null
        }

        // 从开始括号后开始，进行括号匹配
        int depth = 1;  // 已经遇到一个 {，所以初始化深度为1
        int endIndex = startBraceIndex + 1;

        // 遍历剩余部分，处理嵌套的 {}
        for (int i = endIndex; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '{') {
                depth++;  // 深度增加
            } else if (c == '}') {
                depth--;  // 深度减少
                if (depth == 0) {
                    // 当深度为0时，说明匹配完成
                    endIndex = i + 1;
                    break;
                }
            }
        }

        // 如果深度仍然不为0，说明缺失了括号，我们就自动补全
        if (depth > 0) {
            // 假设数据没有错误，手动补充缺少的结束括号
            for (int i = 0; i < depth; i++) {
                input += "}";  // 自动补充缺失的 '}'
            }
            endIndex = input.length();
        }

        // 返回提取的"第五步具体批改"部分的JSON字符串
        String res = input.substring(startIndex, endIndex);
        if (!res.startsWith("\"")) {
            res = "\"" + res;
        }
        res = "{" + res + "}";
        return res;
    }


    @Test
    public void test1() {
        JSONObject text = new JSONObject();
        text.set("text", "As the twins around them in disappointment, their father appeared. Their father knew what they were up to and didn't scold them. Then their father told them to clean up the kitchen and helped them to make some egg sandwiches and woked some porridge. The twins cheered up. They did as told and prepared the breakfast tray and add a card on the tray.\\n\\nThe twins carried the breakfast upstairs and woke their mother up. Then the twins kissed her and shouted \"happy mother's day\". Their mother eyes widened at the sight of the breakfast. She bit into a sandwich. To her surprise that the sandwich was tasted so delicious. She commended twins & it was most delicious breakfast I have ever eaten. Then the living bedroom laughter linger on ground the bedroom.");
        EssayScoreBaseResponse response = essayScoreApi.getScore(JSONUtil.toJsonStr(text));
        System.out.println("转换后的分数" + response.getScore2TotalScore(100F));
    }

    @Test
    public void jsonTest() {
        String str = "{\"isTrue\": false,\"review\": \"学生的计算错误了，斜边长度计算公式应该是\\(c = \\sqrt{a^2 + b^2}\\)，而不是\\(a^2 + b^2 = c\\)。此外，学生在最后一句话中表达得不清楚，可能有打字错误。\"}";
        System.out.println(EscapeUtil.escape(str));
        JSONObject obj = JSONUtil.parseObj(EscapeUtil.escape(str), false);
        System.out.println(obj);
    }

    @Test
    public void jsonTest1() {
        JSONObject obj = new JSONObject();
        obj.set("isTrue", false);
        obj.set("review",
                "学生的计算错误了，斜边长度计算公式应该是\\(c = \\sqrt{a^2 + b^2}\\)，而不是\\(a^2 + b^2 = c\\)。此外，学生在最后一句话中表达得不清楚，可能有打字错误。");
        System.out.println(obj);
    }

    @Test
    public void test2() {
        String matched = ReUtil.getGroup0("[0-9]+", "\\( 2 \\)");
        System.out.println(matched);
    }

    @Test
    public void test3() {
        String regex = "(?<=-(\\s?)学生答案\\n)([\\d|\\D]*?)(?=\\n-)";
        String str = "```\n" +
                "---\n" +
                "- 学生答案\n" +
                "0.43\n" +
                "0.55\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "- 学生答案\n" +
                "0.1\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "- 学生答案\n" +
                "7.0\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "- 学生答案\n" +
                "1\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "- 学生答案\n" +
                "0.62\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "- 学生答案\n" +
                "0.66\n" +
                "- 是否正确\n" +
                "Y\n" +
                "- 批改意见\n" +
                "学生回答正确\n" +
                "---\n" +
                "```";
        System.out.println(ReUtil.findAll(regex, str, 0));
    }

    @Test
    public void test4() {
        String filePath = "/home/<USER>/workspace/chaty/chaty-api/src/test/resource/0bd18a0257114949acbd4dedaefb7d09.pdf";
        JSONObject pdfInfo = PDFUtil.getPDFInfo(filePath);
        System.out.println(pdfInfo.toStringPretty());
    }

    @Test
    public void test5() {
        convertHtmlToPdf("C:\\Users\\<USER>\\Desktop\\chaty\\chaty-api\\src\\main\\resources\\templates\\essayReport.html", "C:\\Users\\<USER>\\Desktop\\chaty\\chaty-api\\src\\main\\resources\\templates\\test.pdf");
    }

    @Resource
    private PasswordEncoder passwordEncoder;

    @Test
    public void test6() {
        String str = passwordEncoder.encode("miaomiao_wangwang");
        System.out.println(str);
    }
}
